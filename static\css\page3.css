.job-details-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    font-family: 'Poppins', sans-serif;
    width: 100%;
}

.detail-section {
    margin-bottom: 20px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: all 0.3s ease;
    background-color: var(--secondary-color);
}

.detail-section:hover {
    box-shadow: 0 8px 15px rgba(0,0,0,0.1);
    transform: translateY(-3px);
}

.edit-button {
    background: none;
    border: none;
    color: var(--primary-blue);
    cursor: pointer;
    float: right;
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.edit-button:hover {
    background-color: #f0f7ff;
    color: var(--primary-pink);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 10px;
}

.section-header h2 {
    color: var(--primary-blue);
    font-weight: 600;
    margin: 0;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-family: 'Poppins', sans-serif;
    transition: all 0.3s ease;
    background-color: var(--text-light);
}

.form-control:focus {
    border-color: var(--primary-blue);
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.15);
    transform: translateY(-1px);
}

.btn-primary {
    background-color: var(--primary-pink);
    color: var(--text-light);
    border: 2px solid var(--primary-pink);
    padding: 10px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    font-family: 'Poppins', sans-serif;
    box-shadow: var(--box-shadow);
}

.btn-primary:hover {
    background-color: white;
    color: var(--primary-pink);
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
}

.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    padding: 30px;
    border-radius: var(--border-radius);
    width: 80%;
    max-width: 500px;
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
    border-top: 5px solid var(--primary-blue);
}

.modal-content h2 {
    color: var(--primary-blue);
    margin-bottom: 20px;
    font-weight: 600;
}

.modal-content button {
    margin-top: 15px;
}

/* Selection toggle styles */
.selection-type-toggle {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.selection-toggle {
    padding: 8px 15px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    background-color: #f5f5f5;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
    text-align: center;
    font-size: 14px;
}

.selection-toggle.active {
    background-color: var(--primary-blue);
    color: white;
    border-color: var(--primary-blue);
}

.selection-toggle:hover:not(.active) {
    background-color: #e0e0e0;
}