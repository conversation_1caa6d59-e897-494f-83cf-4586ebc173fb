
 // Function to generate unique message ID
 function generateMessageId() {
    return Date.now().toString();
}

// Function to get current time
function getCurrentTime() {
    const now = new Date();
    return now.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}

// Function to switch contact and load messages
let currentUserId = null;

document.addEventListener('DOMContentLoaded', function() {
    // Get current user ID from meta tag
    currentUserId = document.querySelector('meta[name="user-id"]')?.content;

    // First, remove any existing active states
    document.querySelectorAll('.contact-item').forEach(item => {
        item.classList.remove('active');
    });

    // Initialize contact previews
    const contacts = document.querySelectorAll('.contact-item');
    contacts.forEach(contact => {
        const userId = contact.dataset.userId;
        const activityType = contact.dataset.activityType;
        const reactorName = contact.dataset.reactorName;
        const lastMessage = contact.dataset.lastMessage;
        const lastMessageSenderId = contact.dataset.lastMessageSenderId;
        const lastActivityTime = contact.dataset.lastActivityTime;

        if (activityType === 'reaction' && reactorName) {
            updateContactPreview(
                userId,
                null,
                lastMessageSenderId,
                lastActivityTime,
                true,
                reactorName
            );
        } else if (lastMessage) {
            updateContactPreview(
                userId,
                lastMessage,
                lastMessageSenderId,
                lastActivityTime
            );
        }
    });

    // Select first contact after previews are initialized
    selectFirstContact();

    // Setup message input handlers
    const messageInput = document.querySelector('.message-input');
    const sendButton = document.querySelector('.send-button');

    if (sendButton && messageInput) {
        sendButton.addEventListener('click', () => {
            sendMessage(messageInput.value);
        });

        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage(messageInput.value);
            }
        });
    }
});

function switchContact(contactElement) {
    if (!contactElement) return;

    // Remove unread style when clicking contact
    contactElement.classList.remove('unread');

    // Clear all active states first
    document.querySelectorAll('.contact-item').forEach(item => {
        item.classList.remove('active');
    });

    // Set the new active contact
    contactElement.classList.add('active');
    contactElement.classList.remove('unread'); // Remove bold when clicking contact

    const userId = contactElement.dataset.userId;
    const userName = contactElement.dataset.userName;
    const chatMessages = document.querySelector('.chat-messages');

    // Update chat header
    const currentChatName = document.getElementById('currentChatName');
    if (currentChatName) {
        currentChatName.textContent = userName;
        currentChatName.style.fontSize = '1.20rem';
    }

    // Check if profile panel is open and update it
    const profilePanel = document.getElementById('profilePanel');
    if (profilePanel && profilePanel.classList.contains('active')) {
        // Update profile photo
        const profileAvatar = document.getElementById('profilePanelAvatar');
        if (profileAvatar) {
            profileAvatar.src = `/get_profile_photo/${userId}`;
            profileAvatar.onerror = function() {
                const initials = userName.split(' ').map(n => n[0]).join('');
                this.src = `https://ui-avatars.com/api/?name=${initials}&background=2563eb&color=fff&size=128`;
            };
        }

        // Update profile name
        const profilePanelName = document.getElementById('profilePanelName');
        if (profilePanelName) {
            profilePanelName.textContent = userName;
        }

        // Fetch and update user details
        fetch(`/get_user_details/${userId}`)
            .then(response => response.json())
            .then(userData => {
                const profileDetails = document.querySelector('.profile-info-section');
                if (profileDetails) {
                    profileDetails.innerHTML = `
                        <div class="profile-photo-wrapper">
                            <img src="/get_profile_photo/${userId}" id="profilePanelAvatar" class="profile-panel-avatar" alt="Profile Photo" onerror="this.src='https://ui-avatars.com/api/?name=${encodeURIComponent(userName)}&background=2563eb&color=fff&size=128'">
                        </div>
                        <h2 class="profile-name-large" id="profilePanelName">${userName}</h2>
                        <div class="profile-status-large">
                            <i class="fas fa-circle text-success" style="font-size: 0.5rem;"></i>
                            <span>Online</span>
                        </div>
                        <div class="profile-details">
                            <div class="profile-menu">
                                <!-- Existing menu items remain unchanged -->
                            </div>
                        </div>`;
                }
            })
            .catch(error => console.error('Error fetching user details:', error));
    }

    // Load messages for the selected contact
    // ... rest of your existing switchContact code ...
    // Update avatar
    const currentChatAvatar = document.getElementById('currentChatAvatar');
    if (currentChatAvatar) {
        currentChatAvatar.src = `/get_profile_photo/${userId}`;
        currentChatAvatar.onerror = function() {
            const initials = userName.split(' ').map(n => n[0]).join('');
            this.src = `https://ui-avatars.com/api/?name=${initials}&background=2563eb&color=fff&size=128`;
        };
    }

    // Show loading state
    chatMessages.innerHTML = '<div class="text-center p-3"><i class="fas fa-spinner fa-spin"></i> Loading messages...</div>';

    // Fetch messages
    fetch(`/get_messages/${userId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            chatMessages.innerHTML = '';

            if (!data.success) {
                throw new Error(data.error || 'Failed to load messages');
            }

            if (!data.messages || !data.messages.length) {
                chatMessages.innerHTML = '<div class="text-center p-3">No messages yet. Start a conversation!</div>';
                return;
            }

            // Render messages
            data.messages.forEach(message => {
                displayNewMessage(message);
            });

            // Scroll to bottom
            chatMessages.scrollTop = chatMessages.scrollHeight;
        })
        .catch(error => {
            console.error('Error:', error);
            chatMessages.innerHTML = `
                <div class="text-center p-3">
                    <div class="alert alert-danger" role="alert">
                        Failed to load messages. Please try again.
                    </div>
                </div>`;
        });

    // Update URL
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.set('selected', userId);
    window.history.pushState({}, '', newUrl);

    // Add this part after fetching messages
    fetch(`/get_user_details/${userId}`)
        .then(response => response.json())
        .then(userData => {
            if (userData.error) {
                console.error('Error fetching user details:', userData.error);
                return;
            }

            // Update contact name if needed
            const userName = `${userData.first_name} ${userData.last_name}`;
            document.getElementById('currentChatName').textContent = userName;

            // Update contact status
            const statusElement = document.getElementById('currentChatStatus');
            if (statusElement) {
                statusElement.innerHTML = `
                    <i class="fas fa-circle ${userData.status === 'online' ? 'text-success' : 'text-secondary'} me-1"
                       style="font-size: 0.5rem;"></i>
                    ${userData.status || 'offline'}
                `;
            }
        })
        .catch(error => console.error('Error:', error));
}

// Helper functions
function escapeHtml(unsafe) {
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

function formatTimestamp(timestamp) {
    if (!timestamp) return '';

    try {
        // Parse the ISO string to a Date object
        const date = new Date(timestamp);

        // Check if date is valid
        if (isNaN(date.getTime())) {
            console.error('Invalid date:', timestamp);
            return new Date().toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
            });
        }

        return date.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
    } catch (error) {
        console.error('Error formatting timestamp:', error);
        // Fallback to current time if there's an error
        return new Date().toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
    }
}

function getStatusIcon(status) {
    switch (status) {
        case 'read': return 'check-double text-primary';
        case 'delivered': return 'check-double';
        case 'sent': return 'check';
        default: return 'check';
    }
}







// Profile Panel Toggle with container adjustment
function toggleProfilePanel() {
    const panel = document.getElementById('profilePanel');
    const container = document.querySelector('.chat-container');
    const chatHeader = document.querySelector('.chat-header');
    const navbarNav = document.querySelector('.navbar-nav.ms-auto');
    const profileContainer = document.querySelector('.profile-container');

    panel.classList.toggle('active');
    container.classList.toggle('panel-active');
    chatHeader.classList.toggle('panel-active');
    navbarNav.classList.toggle('panel-active');
    profileContainer.classList.toggle('panel-active');

    if (panel.classList.contains('active')) {
        const activeContact = document.querySelector('.contact-item.active');
        if (activeContact) {
            const userId = activeContact.dataset.userId;
            const userName = activeContact.dataset.userName;

            // Update profile photo
            const profileAvatar = document.getElementById('profilePanelAvatar');
            profileAvatar.src = `/get_profile_photo/${userId}`;
            profileAvatar.onerror = function() {
                const initials = userName.split(' ').map(n => n[0]).join('');
                this.src = `https://ui-avatars.com/api/?name=${initials}&background=2563eb&color=fff&size=128`;
            };

            // Update profile name
            document.getElementById('profilePanelName').textContent = userName;

            // Fetch and update other user details
            fetch(`/get_user_details/${userId}`)
                .then(response => response.json())
                .then(userData => {
                    updateProfileDetails(userData);
                });
        }
    }
}


// Profile Dropdown Toggle
function toggleProfileDropdown(event) {
    event.stopPropagation();
    const dropdown = document.getElementById('profileDropdown');
    const navbar = document.querySelector('.navbar');

    dropdown.classList.toggle('active');
    navbar.classList.toggle('dropdown-active');

    // Close dropdown when clicking outside
    document.addEventListener('click', function closeDropdown(e) {
        if (!dropdown.contains(e.target) && !event.target.contains(e.target)) {
            dropdown.classList.remove('active');
            navbar.classList.remove('dropdown-active');
            document.removeEventListener('click', closeDropdown);
        }
    });
}







function handleKeyPress(event) {
    const textarea = event.target;
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        const message = textarea.value.trim();
        if (message) {
            sendMessage(message);
        }
    }

    const sendButton = document.querySelector('.send-button');
    sendButton.classList.toggle('active', textarea.value.trim().length > 0);
}

function updateContactPreview(contactId, messageText, senderId, messageTime, isReaction = false, reactorName = null, emoji = null, messageType = null, fileName = null) {
    const contactItem = document.querySelector(`.contact-item[data-user-id="${contactId}"]`);
    if (!contactItem) return;

    const previewTextElement = contactItem.querySelector('.preview-text');
    const currentUserId = document.querySelector('meta[name="user-id"]').content;
    const isCurrentUser = parseInt(senderId) === parseInt(currentUserId);
    const name = isCurrentUser ? 'You' : contactItem.querySelector('.contact-name').textContent.split(' ')[0];

    if (isReaction && reactorName && emoji) {
        // Use the full name for reactions
        previewTextElement.textContent = `${reactorName} reacted ${emoji} to your message`;

        // Update data attributes
        contactItem.dataset.activityType = 'reaction';
        contactItem.dataset.reactorName = reactorName;
        contactItem.dataset.reactionEmoji = emoji;
    } else if (messageType === 'image') {
        // Handle image messages
        previewTextElement.textContent = `${name}: Sent an image${fileName ? ': ' + fileName : ''}`;

        // Update data attributes
        contactItem.dataset.lastMessageType = 'image';
        if (fileName) contactItem.dataset.lastFileName = fileName;
    } else if (messageType && messageType !== 'text') {
        // Handle other file types
        previewTextElement.textContent = `${name}: Sent a file${fileName ? ': ' + fileName : ''}`;

        // Update data attributes
        contactItem.dataset.lastMessageType = messageType;
        if (fileName) contactItem.dataset.lastFileName = fileName;
    } else if (messageText) {
        // Check if this is a reaction message
        if (messageText.includes('reacted') && messageText.includes('to your message')) {
            previewTextElement.textContent = messageText;
        } else {
            previewTextElement.textContent = `${name}: ${messageText}`;

            // Update data attributes
            contactItem.dataset.lastMessageType = 'text';
        }
    }

    // Update last message data
    if (messageText) {
        contactItem.dataset.lastMessage = messageText;
    }

    if (senderId) {
        contactItem.dataset.lastMessageSenderId = senderId;
    }

    // Update timestamp if provided
    if (messageTime) {
        const timeElement = contactItem.querySelector('.message-time');
        if (timeElement) {
            timeElement.textContent = formatTimestamp(messageTime);
        }
        contactItem.dataset.lastActivityTime = messageTime;
    }
}

let isSending = false;

function sendMessage(messageText) {
    if (isSending) return;

    const activeContact = document.querySelector('.contact-item.active');
    if (!activeContact) return;

    const receiverId = activeContact.dataset.userId;
    const fileInput = document.querySelector('input[type="file"]');
    const chatInput = document.querySelector('.chat-input');

    // Get reply data if exists
    const replyContainer = document.querySelector('.reply-container');
    const replyToId = replyContainer ? replyContainer.dataset.replyToId : null;
    const repliedMessageText = replyContainer ? replyContainer.querySelector('.reply-text').textContent : null;

    // Handle file uploads
    if (fileInput && fileInput.files.length > 0) {
        const file = fileInput.files[0];
        const MAX_FILE_SIZE = 25 * 1024 * 1024; // 25MB

        // Validate file size
        if (file.size > MAX_FILE_SIZE) {
            alert(`File size must be less than 25MB. Current size: ${(file.size / (1024 * 1024)).toFixed(2)}MB`);
            return;
        }

        isSending = true;
        const formData = new FormData();

        formData.append('file', file);
        formData.append('receiver_id', receiverId);
        formData.append('message_text', messageText || `Sent a file: ${file.name}`);

        // Add reply data if exists
        if (replyToId && repliedMessageText) {
            formData.append('reply_to_id', replyToId);
            formData.append('replied_message_text', repliedMessageText);
        }

        // Show loading state
        const sendButton = document.querySelector('.send-button');
        if (sendButton) {
            sendButton.disabled = true;
            sendButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        }

        fetch('/send_file_message', {
            method: 'POST',
            body: formData,
            headers: {
                'Accept': 'application/json'
            }
        })
        .then(async response => {
            const result = await response.json();
            if (!response.ok) {
                throw new Error(result.error || `HTTP error! status: ${response.status}`);
            }
            return result;
        })
        .then(result => {
            if (result.success) {
                // Clear file input and preview
                fileInput.value = '';
                const previewContainer = document.querySelector('.file-preview-container');
                if (previewContainer) previewContainer.remove();

                // Clear reply container if exists
                if (replyContainer) replyContainer.remove();

                // Update UI to show message was sent
                const currentUser = getCurrentUserInfo();

                // Check if this is an image file
                const isImage = file.type.startsWith('image/');
                const previewText = isImage ?
                    (messageText || `Sent an image: ${file.name}`) :
                    (messageText || `Sent a file: ${file.name}`);

                updateContactPreview(
                    receiverId,
                    previewText,
                    currentUser.id,
                    new Date().toISOString(),
                    false,  // isReaction
                    null,   // reactorName
                    null,   // emoji
                    isImage ? 'image' : 'file',
                    file.name
                );

                // Auto-open image viewer for image files
                if (isImage && result.message_id) {
                    // Use setTimeout to ensure the message is rendered
                    setTimeout(() => {
                        openImageViewer(result.message_id, file.name);
                    }, 500);
                }
            } else {
                throw new Error(result.error || 'Failed to send file');
            }
        })
        .catch(error => {
            console.error('Error sending file:', error);
            alert(`Failed to send file: ${error.message}`);
        })
        .finally(() => {
            isSending = false;
            // Reset send button
            if (sendButton) {
                sendButton.disabled = false;
                sendButton.innerHTML = '<i class="fas fa-paper-plane"></i>';
            }
        });

        return;
    }

    // Handle text message (existing code)
    if (messageText && messageText.trim()) {
        const currentUser = getCurrentUserInfo();

        const messageData = {
            receiver_id: receiverId,
            message: messageText,
            sender_id: currentUser.id,
            sender_name: `${currentUser.firstName} ${currentUser.lastName}`,
            sender_type: currentUser.type
        };

        // Add reply data if exists
        if (replyToId && repliedMessageText) {
            messageData.reply_to_id = replyToId;
            messageData.replied_message_text = repliedMessageText;
        }

        socket.emit('send_message', messageData);

        updateContactPreview(receiverId, messageText, currentUser.id);
        chatInput.value = '';
        chatInput.style.height = 'auto';

        // Remove reply container if exists
        if (replyContainer) {
            replyContainer.remove();
        }
    }
}

function displayNewMessage(message) {
    console.log('Displaying message with ID:', message.id);

    removeSelectedFile();
    const chatMessages = document.querySelector('.chat-messages');
    const currentUserId = document.querySelector('meta[name="user-id"]').content;
    const isSentByMe = parseInt(message.sender_id) === parseInt(currentUserId);

    const previousMessage = chatMessages.lastElementChild;
    const isConsecutive = previousMessage &&
        previousMessage.getAttribute('data-sender-id') === message.sender_id.toString();

    // Check if message is deleted by looking at its text
    const isDeletedMessage = message.message_text === 'Message unsent';

    // Handle message text with proper styling for deleted messages
    let messageTextHtml = '';
    if (isDeletedMessage) {
        messageTextHtml = `<p class="message-text deleted-message">Message unsent</p>`;
    } else if (message.message_text) {
        messageTextHtml = `<p class="message-text">${escapeHtml(message.message_text)}</p>`;
    }

    // Rest of display logic - only show attachments, reactions, etc. for non-deleted messages
    let fileAttachment = '';
    if (!isDeletedMessage && message.file_name) {
        if (message.message_type === 'image') {
            // For images, show the actual image preview
            fileAttachment = `
                <div class="file-attachment">
                    <img src="/get_message_file/${message.id}"
                         alt="${message.file_name}"
                         class="message-image"
                         style="max-width: 200px; max-height: 200px; border-radius: 8px; cursor: pointer;"
                         onclick="openImageViewer(${message.id}, '${message.file_name}')"
                         data-message-id="${message.id}"
                         data-file-name="${message.file_name}">
                </div>
            `;

            // Auto-open image viewer for new image messages
            if (message.auto_open_image) {
                // Use setTimeout to ensure the DOM is ready
                setTimeout(() => {
                    openImageViewer(message.id, message.file_name);
                }, 300);
            }
        } else {
            // For other files, keep the download button
            const fileIcon = getFileIconClass(message.message_type);
            fileAttachment = `
                <div class="file-attachment" onclick="downloadFile(${message.id}, '${message.file_name}')">
                    <i class="fas ${fileIcon}"></i>
                    <span class="file-name">${escapeHtml(message.file_name)}</span>
                    <i class="fas fa-download"></i>
                </div>
            `;
        }
    }

    // Add reply preview only for non-deleted messages
    let replyPreview = '';
    if (!isDeletedMessage && message.reply_to_id && message.replied_message_text) {
        replyPreview = `
            <div class="replied-message" data-reply-to-id="${message.reply_to_id}" style="cursor: pointer;">
                <div class="replied-content">
                    <i class="fas fa-reply"></i>
                    <span>${escapeHtml(message.replied_message_text)}</span>
                </div>
            </div>
        `;
    }

    // Create the message HTML with special class for deleted messages
    const messageHtml = `
    <div class="message ${isSentByMe ? 'message-sent' : 'message-received'} ${isDeletedMessage ? 'message-deleted' : ''}"
         data-message-id="${message.id}"
         data-sender-id="${message.sender_id}">
        ${!isConsecutive ? `
            <div class="message-avatar">
                <img src="/get_profile_photo/${message.sender_id}"
                     alt="Avatar"
                     onerror="this.src='static/default-avatar.png'">
            </div>
        ` : ''}

        <div class="message-wrapper">
            ${replyPreview}
            <div class="message-content">
                ${messageTextHtml}
                ${fileAttachment}
                <div class="message-reactions">
                    ${!isDeletedMessage && message.reactions && message.reactions.length > 0 ?
                        message.reactions.map(reaction => `
                            <span class="message-reaction"
                                data-user-id="${reaction.user_id}"
                                data-user-type="${reaction.user_type}"
                                title="${reaction.user_name}">
                                ${reaction.emoji}
                            </span>
                        `).join('') : ''}
                </div>
                <div class="message-meta">
                    <span class="message-time">${formatTimestamp(new Date(message.timestamp))}</span>
                    ${isSentByMe && !isDeletedMessage ? `
                        <span class="message-status">
                            <i class="fas fa-${getStatusIcon(message.status || 'sent')}"></i>
                        </span>
                    ` : ''}
                </div>
            </div>
        </div>

        <!-- Only show buttons for non-deleted messages -->
        ${!isDeletedMessage ? `
        <div class="reaction-buttons"
             style="position: absolute; top: 50%; ${isSentByMe ? 'left: -95px;' : 'right: -65px;'} transform: translateY(-50%); display: flex; opacity: 0; visibility: hidden; transition: opacity 0.2s, visibility 0.2s;">
            ${isSentByMe ? `
                <button class="delete-btn" onclick="deleteMessage(${message.id})" style="cursor: pointer;">
                    <i class="fas fa-trash"></i>
                </button>
            ` : ''}
            <button class="reply-btn" onclick="startReply(${message.id}, '${escapeHtml(message.message_text || '')}')">
                <i class="fas fa-reply"></i>
            </button>
            <button class="reaction-btn">
                <i class="fas fa-smile"></i>
            </button>
            <div class="emoji-picker" style="display: none; position: absolute; bottom: 40px; right: -10px; transform: translateX(-20%); width: 290px; background: white; border-radius: 8px; box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15); z-index: 9999; padding: 8px 16px;">
                <div class="quick-reactions" style="display: flex; align-items: center; gap: 16px; justify-content: space-evenly;">
                    <span class="emoji-option">😄</span>
                    <span class="emoji-option">😂</span>
                    <span class="emoji-option">❤️</span>
                    <span class="emoji-option">👍</span>
                    <span class="emoji-option">🔥</span>
                    <button class="plus-reaction-button" style="margin-left: 6px;">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
        </div>
        ` : ''}
    </div>`;

    chatMessages.insertAdjacentHTML('beforeend', messageHtml);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// Show reaction buttons only on hover
const chatMessages = document.querySelector('.chat-messages');
chatMessages.addEventListener('mouseover', function(event) {
    const messageElement = event.target.closest('.message');
    if (messageElement) {
        const reactionButtons = messageElement.querySelector('.reaction-buttons');
        if (reactionButtons) {
            reactionButtons.style.opacity = '1';
            reactionButtons.style.visibility = 'visible';
        }
    }
});

chatMessages.addEventListener('mouseout', function(event) {
    const messageElement = event.target.closest('.message');
    if (messageElement) {
        const reactionButtons = messageElement.querySelector('.reaction-buttons');
        if (reactionButtons) {
            const emojiPicker = reactionButtons.querySelector('.emoji-picker');
            // Only hide if emoji picker is not open
            if (!emojiPicker || emojiPicker.style.display !== 'block') {
                reactionButtons.style.opacity = '0';
                reactionButtons.style.visibility = 'hidden';
            }
        }
    }
});

// Close emoji picker when clicking outside
document.addEventListener('click', function(event) {
    const isEmojiPickerClick = event.target.closest('.emoji-picker');
    const isReactionButtonClick = event.target.closest('.reaction-btn');

    if (!isEmojiPickerClick && !isReactionButtonClick) {
        const emojiPickers = document.querySelectorAll('.emoji-picker');
        emojiPickers.forEach(picker => {
            picker.style.display = 'none';
            // Also hide the reaction buttons when closing emoji picker
            const reactionButtons = picker.closest('.reaction-buttons');
            if (reactionButtons) {
                reactionButtons.style.opacity = '0';
                reactionButtons.style.visibility = 'hidden';
            }
        });
        document.querySelectorAll('.reaction-btn').forEach(btn => btn.classList.remove('emoji-picker-open'));
    }
});

// Emoji picker logic
chatMessages.addEventListener('click', function(event) {
    const reactionBtn = event.target.closest('.reaction-btn');
    if (reactionBtn) {
        const emojiPicker = reactionBtn.nextElementSibling;

        document.querySelectorAll('.emoji-picker').forEach(picker => {
            if (picker !== emojiPicker) picker.style.display = 'none';
        });

        emojiPicker.style.display = emojiPicker.style.display === 'none' ? 'block' : 'none';
        reactionBtn.classList.toggle('emoji-picker-open', emojiPicker.style.display === 'block');

        event.stopPropagation();
    }

    if (event.target.classList.contains('emoji-option')) {
        const messageElement = event.target.closest('.message');
        const messageId = messageElement.dataset.messageId;
        const emoji = event.target.textContent;

        // Add/update reaction to the message
        addReactionToMessage(messageId, emoji);

        // Close the emoji picker
        const emojiPicker = event.target.closest('.emoji-picker');
        emojiPicker.style.display = 'none';
    }
});

// Function to add reaction to message
function addReactionToMessage(messageId, emoji) {
    const currentUser = getCurrentUserInfo();
    const messageElement = document.querySelector(`.message[data-message-id="${messageId}"]`);

    if (messageElement) {
        const emojiPicker = messageElement.querySelector('.emoji-picker');
        if (emojiPicker) {
            emojiPicker.style.display = 'none';
        }

        // Check if we already have this reaction
        const reactionsContainer = messageElement.querySelector('.message-reactions');
        const existingReaction = reactionsContainer.querySelector(
            `.message-reaction[data-user-id="${currentUser.id}"][data-user-type="${currentUser.type}"]`
        );

        // Use Socket.IO to send the reaction
        socket.emit('reaction', {
            message_id: messageId,
            emoji: emoji,
            action: 'add'
        });

        // Show immediate feedback with animation
        if (existingReaction) {
            // Update existing reaction with animation
            existingReaction.textContent = emoji;
            existingReaction.classList.add('reaction-updated');
            setTimeout(() => {
                existingReaction.classList.remove('reaction-updated');
            }, 1000);
        } else {
            // Create a temporary reaction element with animation
            const tempReaction = document.createElement('span');
            tempReaction.className = 'message-reaction reaction-added';
            tempReaction.textContent = emoji;
            tempReaction.dataset.userId = currentUser.id;
            tempReaction.dataset.userType = currentUser.type;
            tempReaction.title = `${currentUser.firstName} ${currentUser.lastName}`;
            reactionsContainer.appendChild(tempReaction);
        }
    }
}

// Update reaction click handler to remove reactions
document.addEventListener('click', function(event) {
    if (event.target.classList.contains('message-reaction')) {
        const messageElement = event.target.closest('.message');
        if (!messageElement) return;

        const messageId = messageElement.dataset.messageId;
        const currentUser = getCurrentUserInfo();
        const clickedReaction = event.target;

        // Check if this is the current user's reaction
        if (clickedReaction.dataset.userId === currentUser.id &&
            clickedReaction.dataset.userType === currentUser.type) {

            // Add visual feedback
            clickedReaction.classList.add('reaction-removing');

            // Use Socket.IO to remove the reaction
            socket.emit('reaction', {
                message_id: messageId,
                emoji: clickedReaction.textContent,
                action: 'remove'
            });
        }
    }
});

// Close emoji picker when clicking outside
document.addEventListener('click', function(event) {
    const isEmojiPickerClick = event.target.closest('.emoji-picker');
    const isReactionButtonClick = event.target.closest('.reaction-btn');

    if (!isEmojiPickerClick && !isReactionButtonClick) {
        document.querySelectorAll('.emoji-picker').forEach(picker => {
            picker.style.display = 'none';
        });
        document.querySelectorAll('.reaction-btn').forEach(btn => btn.classList.remove('emoji-picker-open'));
    }
});

function loadMessages(contactId) {
    fetch(`/get_messages/${contactId}`)
        .then(response => response.json())
        .then(data => {
            const chatMessages = document.querySelector('.chat-messages');
            chatMessages.innerHTML = '';

            data.messages.forEach((message, index) => {
                const isSentByMe = message.sender_id === getCurrentUserInfo().id;
                const isConsecutive = index > 0 && data.messages[index - 1].sender_id === message.sender_id;

                let messageContent = '';
                if (message.file_type && message.file_type.startsWith('image/')) {
                    messageContent = `
                        <div class="message-wrapper">
                            <div class="message-content">
                                ${message.message_text ? `<p class="message-text">${message.message_text}</p>` : ''}
                                <div class="file-attachment">
                                    <img src="/get_message_file/${message.id}"
                                         alt="Image"
                                         class="attached-image">
                                </div>
                                <div class="message-meta">
                                    <span class="message-time">${formatTimestamp(message.timestamp)}</span>
                                    ${isSentByMe ? `<span class="message-status"><i class="fas fa-check"></i></span>` : ''}
                                </div>
                            </div>
                        </div>`;
                } else if (message.file_type) {
                    // Handle other file types...
                    messageContent = `
                        <div class="message-wrapper">
                            <div class="message-content">
                                ${message.message_text ? `<p class="message-text">${message.message_text}</p>` : ''}
                                <div class="file-attachment">
                                    <div class="file-preview">
                                        <i class="fas ${getFileIcon(message.file_name.split('.').pop())}"></i>
                                        <span class="file-name">${message.file_name}</span>
                                        <span class="file-size">${formatFileSize(message.file_size)}</span>
                                    </div>
                                    <a href="/get_message_file/${message.id}" class="file-download" download="${message.file_name}">
                                        <i class="fas fa-download"></i>
                                    </a>
                                </div>
                                <div class="message-meta">
                                    <span class="message-time">${formatTimestamp(message.timestamp)}</span>
                                    ${isSentByMe ? `<span class="message-status"><i class="fas fa-check"></i></span>` : ''}
                                </div>
                            </div>
                        </div>`;
                } else {
                    // Handle text messages...
                    messageContent = `
                        <div class="message-wrapper">
                            <div class="message-content">
                                <p class="message-text">${message.message_text}</p>
                                <div class="message-meta">
                                    <span class="message-time">${formatTimestamp(message.timestamp)}</span>
                                    ${isSentByMe ? `<span class="message-status"><i class="fas fa-check"></i></span>` : ''}
                                </div>
                            </div>
                        </div>`;
                }

                const messageElement = document.createElement('div');
                messageElement.className = `message ${isSentByMe ? 'message-sent' : 'message-received'}`;
                messageElement.dataset.messageId = message.id;
                messageElement.innerHTML = `
                    ${!isConsecutive ? `
                        <div class="message-avatar">
                            <img src="/get_profile_photo/${message.sender_id}"
                                 alt="Avatar"
                                 onerror="this.src='static/default-avatar.png'">
                        </div>
                    ` : ''}
                    ${messageContent}
                `;

                chatMessages.appendChild(messageElement);
            });

            chatMessages.scrollTop = chatMessages.scrollHeight;
        })
        .catch(error => console.error('Error loading messages:', error));
}

function getFileIconClass(fileType) {
    const iconMap = {
        'application/pdf': 'fa-file-pdf',
        'application/msword': 'fa-file-word',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'fa-file-word',
        'application/vnd.ms-excel': 'fa-file-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'fa-file-excel',
        'image/': 'fa-file-image',
        'video/': 'fa-file-video',
        'audio/': 'fa-file-audio',
        'text/': 'fa-file-alt',
        'application/zip': 'fa-file-archive',
        'application/x-rar-compressed': 'fa-file-archive'
    };

    for (const [type, icon] of Object.entries(iconMap)) {
        if (fileType.startsWith(type)) {
            return icon;
        }
    }
    return 'fa-file';
}

function getFileIconColor(fileType) {
    if (fileType.startsWith('image/')) return '#22c55e';  // Green
    if (fileType.startsWith('video/')) return '#ef4444';  // Red
    if (fileType.startsWith('audio/')) return '#f59e0b';  // Orange
    if (fileType.startsWith('application/pdf')) return '#dc2626';  // Red
    if (fileType.includes('word')) return '#2563eb';  // Blue
    if (fileType.includes('excel')) return '#16a34a';  // Green
    if (fileType.includes('powerpoint')) return '#ea580c';  // Orange
    if (fileType.includes('archive') || fileType.includes('zip')) return '#7c3aed';  // Purple
    if (fileType.includes('code') || fileType.includes('text')) return '#64748b';  // Gray
    return '#6b7280';  // Default gray
}

function downloadFile(messageId, fileName) {
    // Create a temporary anchor element
    const link = document.createElement('a');
    link.href = `/get_message_file/${messageId}`;
    link.download = fileName; // This will suggest the filename to save as
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Function to select first contact
function selectFirstContact() {
    // Remove active class from all contacts first
    document.querySelectorAll('.contact-item').forEach(item => {
        item.classList.remove('active');
    });

    // Get and activate the first contact
    const firstContact = document.querySelector('.contacts-body .contact-item:first-child');
    if (firstContact) {
        firstContact.classList.add('active');
        switchContact(firstContact);
    }
}

// Function to update contact preview and order
function updateContactOrder(userId, messageText, senderId) {
    const contactItem = document.querySelector(`.contact-item[data-user-id="${userId}"]`);
    if (!contactItem) return;

    // Update preview
    updateContactPreview(userId, messageText, senderId);

    // Move to top of list
    const contactsList = document.querySelector('.contacts-body');
    if (contactsList && contactsList.firstChild !== contactItem) {
        contactsList.insertBefore(contactItem, contactsList.firstChild);
        selectFirstContact();
    }
}

// Initialize with first contact
document.querySelector('.contact-item').click();





// Initialize Socket.IO
const socket = io();

// At the top of your file after socket initialization
window.active_users = {};

// Initialize active users when page loads
fetch('/get_active_users')
    .then(response => response.json())
    .then(users => {
        users.forEach(userId => {
            window.active_users[userId] = true;
        });
        updateAllUserStatuses();
    });

socket.on('user_status_change', function(data) {
    if (data.status === 'online') {
        window.active_users[data.user_id] = true;
    } else {
        delete window.active_users[data.user_id];
    }
    updateAllUserStatuses();
});

function updateAllUserStatuses() {
    // Update status for all contacts
    document.querySelectorAll('.contact-item').forEach(contact => {
        const userId = contact.dataset.userId;
        const isOnline = window.active_users[userId];

        // Update status dot if it exists
        const statusDot = contact.querySelector('.status-indicator');
        if (statusDot) {
            statusDot.className = `status-indicator ${isOnline ? 'online' : 'offline'}`;
        }

        // Update status in chat header if this is the active contact
        if (contact.classList.contains('active')) {
            const statusElement = document.getElementById('currentChatStatus');
            if (statusElement) {
                statusElement.innerHTML = `
                    <i class="fas fa-circle ${isOnline ? 'text-success' : 'text-secondary'} me-1"
                       style="font-size: 0.5rem;"></i>
                    ${isOnline ? 'online' : 'offline'}
                `;
            }
        }
    });
}

// Add this to your existing socket connection event
socket.on('connect', function() {
    if (window.currentUserId) { // Make sure you set this somewhere when user logs in
        socket.emit('user_connected', { user_id: window.currentUserId });
    }
});

// Handle new messages from WebSocket
socket.on('new_message', function(message) {
    const contactId = message.sender_id === parseInt(getCurrentUserInfo().id) ?
                     message.receiver_id : message.sender_id;

    const contactsList = document.querySelector('.contacts-body');
    const contactElement = document.querySelector(`.contact-item[data-user-id="${contactId}"]`);
    if (contactsList && contactElement) {
        contactsList.insertBefore(contactElement, contactsList.firstChild);
    }
});

// Handle new messages from WebSocket
socket.on('new_message', function(message) {
    const currentUser = getCurrentUserInfo();
    const activeContactId = document.querySelector('.contact-item.active')?.dataset.userId;
    const contactId = message.sender_id === parseInt(currentUser.id) ?
                     message.receiver_id : message.sender_id;

    // Add unread class only for new messages from others
    if (message.sender_id !== parseInt(currentUser.id)) {
        const contactElement = document.querySelector(`.contact-item[data-user-id="${contactId}"]`);
        if (contactElement && !contactElement.classList.contains('active')) {
            contactElement.classList.add('unread');
        }
    }

    // Check if this is an image message
    const isImageMessage = message.message_type === 'image';

    // Add auto_open_image flag for new image messages
    if (isImageMessage) {
        message.auto_open_image = true;
    }

    // Display message if it's the active conversation
    if (activeContactId && parseInt(activeContactId) === parseInt(contactId)) {
        displayNewMessage(message);
    }

    // Update preview text based on message type
    let previewText = message.message_text;
    if (isImageMessage) {
        previewText = message.message_text || `Sent an image: ${message.file_name}`;
    }

    // Update preview and move contact to top
    updateContactPreview(
        contactId,
        previewText,
        message.sender_id,
        message.timestamp,
        false,  // isReaction
        null,   // reactorName
        null,   // emoji
        message.message_type,
        message.file_name
    );
});

socket.on('message_deleted', function(data) {
    // Update message in chat window
    const messageElement = document.querySelector(`.message[data-message-id="${data.message_id}"]`);
    if (messageElement) {
        // Keep the existing message-meta div with its time
        const existingMeta = messageElement.querySelector('.message-meta');
        const existingTime = existingMeta ? existingMeta.querySelector('.message-time').outerHTML :
            `<span class="message-time">${formatTimestamp(data.timestamp)}</span>`;

        const messageContent = messageElement.querySelector('.message-content');
        if (messageContent) {
            messageContent.innerHTML = `
                <p class="message-text deleted-message">Message unsent</p>
                <div class="message-meta">
                    ${existingTime}
                </div>
            `;
        }

        // Add deleted message class
        messageElement.classList.add('message-deleted');

        // Remove reaction buttons and other interactive elements
        const reactionButtons = messageElement.querySelector('.reaction-buttons');
        if (reactionButtons) {
            reactionButtons.remove();
        }

        // Remove any existing reactions
        const reactionsContainer = messageElement.querySelector('.message-reactions');
        if (reactionsContainer) {
            reactionsContainer.innerHTML = '';
        }
    }

    // Update contact preview
    const currentUserId = getCurrentUserInfo().id;
    const isSender = parseInt(currentUserId) === parseInt(data.sender_id);
    const contactId = isSender ? data.receiver_id : data.sender_id;

    const contactItem = document.querySelector(`.contact-item[data-user-id="${contactId}"]`);
    if (contactItem) {
        const previewTextElement = contactItem.querySelector('.preview-text');
        if (previewTextElement) {
            const senderName = isSender ? 'You' : contactItem.querySelector('.contact-name').textContent.split(' ')[0];
            previewTextElement.textContent = `${senderName}: Message unsent`;
            previewTextElement.classList.add('unsent-preview-text');
        }

        // Update timestamp in contact preview
        const timeElement = contactItem.querySelector('.message-time');
        if (timeElement) {
            timeElement.textContent = formatTimestamp(data.timestamp);
        }
    }
});

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Get current user ID from meta tag
    currentUserId = document.querySelector('meta[name="user-id"]')?.content;

    // First, remove any existing active states
    document.querySelectorAll('.contact-item').forEach(item => {
        item.classList.remove('active');
    });

    // Initialize contact previews
    const contacts = document.querySelectorAll('.contact-item');
    contacts.forEach(contact => {
        const userId = contact.dataset.userId;
        const activityType = contact.dataset.activityType;
        const reactorName = contact.dataset.reactorName;
        const lastMessage = contact.dataset.lastMessage;
        const lastMessageSenderId = contact.dataset.lastMessageSenderId;
        const lastActivityTime = contact.dataset.lastActivityTime;
        const lastMessageType = contact.dataset.lastMessageType;
        const lastFileName = contact.dataset.lastFileName;

        if (activityType === 'reaction' && reactorName) {
            updateContactPreview(
                userId,
                null,
                lastMessageSenderId,
                lastActivityTime,
                true,
                reactorName
            );
        } else if (lastMessage) {
            updateContactPreview(
                userId,
                lastMessage,
                lastMessageSenderId,
                lastActivityTime,
                false,  // isReaction
                null,   // reactorName
                null,   // emoji
                lastMessageType,
                lastFileName
            );
        }
    });

    // Select first contact after previews are initialized
    selectFirstContact();

    // Setup message input handlers
    const messageInput = document.querySelector('.message-input');
    const sendButton = document.querySelector('.send-button');

    if (sendButton && messageInput) {
        sendButton.addEventListener('click', () => {
            sendMessage(messageInput.value);
        });

        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage(messageInput.value);
            }
        });
    }
});

document.addEventListener('DOMContentLoaded', function() {
    const picker = document.querySelector('emoji-picker');
    const trigger = document.querySelector('.emoji-trigger');
    const input = document.querySelector('.chat-input');

    trigger.addEventListener('click', (e) => {
        e.stopPropagation();
        picker.style.display = picker.style.display === 'none' ? 'block' : 'none';
    });

    picker.addEventListener('emoji-click', event => {
        input.value += event.detail.unicode;
        picker.style.display = 'none';
    });

    // Close picker when clicking outside
    document.addEventListener('click', (e) => {
        if (!picker.contains(e.target) && !trigger.contains(e.target)) {
            picker.style.display = 'none';
        }
    });
});

// Add event listener for input changes
document.addEventListener('DOMContentLoaded', function() {
    const messageInput = document.querySelector('.message-input');
    let typingTimer;

    if (messageInput) {
        messageInput.addEventListener('input', () => {
            const activeContact = document.querySelector('.contact-item.active');
            if (!activeContact) return;

            const receiverId = activeContact.dataset.userId;

            console.log('Emitting typing event to:', receiverId); // Debug log
            socket.emit('typing', { receiver_id: receiverId });

            clearTimeout(typingTimer);

            typingTimer = setTimeout(() => {
                console.log('Emitting stop typing event to:', receiverId); // Debug log
                socket.emit('stop_typing', { receiver_id: receiverId });
            }, 1000);
        });
    }
});

// Function to show typing indicator
function showTypingIndicator(username, senderId) {
    const activeContact = document.querySelector('.contact-item.active');
    if (!activeContact) return;

    // Only show typing indicator if we're in the conversation with the typing user
    if (parseInt(activeContact.dataset.userId) === parseInt(senderId)) {
        const indicator = document.getElementById('typing');
        if (indicator) {
            indicator.querySelector('i').textContent = `${username} is typing...`;
            indicator.style.display = 'block';
        }
    }
}

// Function to hide typing indicator
function hideTypingIndicator(senderId) {
    const activeContact = document.querySelector('.contact-item.active');
    if (!activeContact) return;

    // Only hide typing indicator if it's from the current active conversation
    if (parseInt(activeContact.dataset.userId) === parseInt(senderId)) {
        const indicator = document.getElementById('typing');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }
}

// Socket.IO event handlers
socket.on('user_typing', function(data) {
    showTypingIndicator(data.sender_name, data.sender_id);
});

socket.on('user_stop_typing', function(data) {
    hideTypingIndicator(data.sender_id);
});

// Add input event listener to detect when current user is typing
const chatInput = document.querySelector('.chat-input');
let typingTimer;

chatInput.addEventListener('input', () => {
    const activeContact = document.querySelector('.contact-item.active');
    if (!activeContact) return;

    clearTimeout(typingTimer);

    // Emit typing event with the current active contact's ID
    socket.emit('typing', {
        receiver_id: activeContact.dataset.userId
    });

    // Stop typing after 1 second of no input
    typingTimer = setTimeout(() => {
        socket.emit('stop_typing', {
            receiver_id: activeContact.dataset.userId
        });
    }, 1000);
});

function getCurrentUserInfo() {
    return {
        id: document.querySelector('meta[name="user-id"]')?.content,
        firstName: document.querySelector('meta[name="user-first-name"]')?.content,
        lastName: document.querySelector('meta[name="user-last-name"]')?.content,
        type: document.querySelector('meta[name="user-type"]')?.content
    };
}

function handleMessageWrapping(messageElement) {
    const textElement = messageElement.querySelector('.message-text');
    const contentElement = messageElement.querySelector('.message-content');

    if (!textElement || !contentElement) return;

    // Reset to check natural width
    textElement.classList.remove('wrapped');

    // Check if content width exceeds maximum allowed width
    const maxWidth = contentElement.offsetWidth;
    const naturalWidth = textElement.scrollWidth;

    if (naturalWidth > maxWidth) {
        textElement.classList.add('wrapped');
    }
}

// Apply to all messages when page loads
document.addEventListener('DOMContentLoaded', () => {
    const messages = document.querySelectorAll('.message');
    messages.forEach(handleMessageWrapping);
});

// Apply when new messages are added
function createMessageHTML(messageData) {
    // ... existing message creation code ...

    const messageElement = document.createElement('div');
    messageElement.className = `message ${messageData.isSent ? 'message-sent' : 'message-received'}`;
    // ... rest of your message creation code ...

    // Apply wrapping after message is added to DOM
    handleMessageWrapping(messageElement);

    return messageElement;
}

// Optional: Handle window resize
let resizeTimeout;
window.addEventListener('resize', () => {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
        const messages = document.querySelectorAll('.message');
        messages.forEach(handleMessageWrapping);
    }, 100);
});

// Add CSS animations for reactions
const style = document.createElement('style');
style.textContent = `
    @keyframes popIn {
        0% { transform: scale(0); }
        70% { transform: scale(1.2); }
        100% { transform: scale(1); }
    }

    @keyframes reactionAdded {
        0% { transform: scale(0); opacity: 0; }
        50% { transform: scale(1.5); opacity: 1; }
        100% { transform: scale(1); opacity: 1; }
    }

    @keyframes reactionUpdated {
        0% { transform: scale(1); }
        50% { transform: scale(1.5); background-color: rgba(255, 255, 0, 0.3); }
        100% { transform: scale(1); }
    }

    @keyframes reactionRemoving {
        0% { transform: scale(1); opacity: 1; }
        100% { transform: scale(0); opacity: 0; }
    }

    .reaction-added {
        animation: reactionAdded 0.5s ease-out;
    }

    .reaction-updated {
        animation: reactionUpdated 0.5s ease-out;
    }

    .reaction-removing {
        animation: reactionRemoving 0.3s ease-out forwards;
    }

    .message-reaction {
        cursor: pointer;
        transition: transform 0.2s ease;
        display: inline-block;
        margin-right: 4px;
        font-size: 1.2em;
    }

    .message-reaction:hover {
        transform: scale(1.2);
    }
`;
document.head.appendChild(style);

// Add this with your other socket listeners
socket.on('reaction_update', function(data) {
    // Update the message reactions if we're in the same conversation
    const messageElement = document.querySelector(`.message[data-message-id="${data.message_id}"]`);
    if (messageElement) {
        const reactionsContainer = messageElement.querySelector('.message-reactions');

        // Clear existing reactions and rebuild from the complete list
        reactionsContainer.innerHTML = '';

        // If we have all_reactions data, use it to rebuild the reactions container
        if (data.all_reactions && data.all_reactions.length > 0) {
            data.all_reactions.forEach(reaction => {
                const reactionElement = document.createElement('span');
                reactionElement.className = 'message-reaction';
                reactionElement.textContent = reaction.emoji;
                reactionElement.dataset.userId = reaction.user_id;
                reactionElement.dataset.userType = reaction.user_type;
                reactionElement.title = reaction.user_name;

                // Add animation class based on action and if it's from the current user
                const currentUser = getCurrentUserInfo();
                if (reaction.user_id == currentUser.id && reaction.user_type == currentUser.type) {
                    if (data.action === 'add') {
                        reactionElement.classList.add('reaction-added');
                        setTimeout(() => {
                            reactionElement.classList.remove('reaction-added');
                        }, 1000);
                    }
                }

                reactionsContainer.appendChild(reactionElement);
            });
        } else if (data.action === 'add') {
            // Fallback to old behavior if all_reactions is not provided
            // Check if reaction from this user already exists
            const existingReaction = reactionsContainer.querySelector(
                `.message-reaction[data-user-id="${data.user_id}"][data-user-type="${data.user_type}"]`
            );

            if (existingReaction) {
                // Update existing reaction with animation
                existingReaction.textContent = data.emoji;
                existingReaction.classList.add('reaction-updated');
                setTimeout(() => {
                    existingReaction.classList.remove('reaction-updated');
                }, 1000);
            } else {
                // Add new reaction with animation
                const reactionElement = document.createElement('span');
                reactionElement.className = 'message-reaction reaction-added';
                reactionElement.textContent = data.emoji;
                reactionElement.dataset.userId = data.user_id;
                reactionElement.dataset.userType = data.user_type;
                reactionElement.title = data.user_name;
                reactionsContainer.appendChild(reactionElement);

                setTimeout(() => {
                    reactionElement.classList.remove('reaction-added');
                }, 1000);
            }
        } else if (data.action === 'remove') {
            // Remove the reaction
            const reactionToRemove = reactionsContainer.querySelector(
                `.message-reaction[data-user-id="${data.user_id}"][data-user-type="${data.user_type}"]`
            );

            if (reactionToRemove) {
                reactionToRemove.classList.add('reaction-removing');
                setTimeout(() => {
                    reactionToRemove.remove();
                }, 300);
            }
        }
    }

    // Always update the contact preview regardless of active conversation
    const currentUser = getCurrentUserInfo();
    const messageSenderId = data.original_sender_id;

    // Only update preview if the original message was sent by current user
    if (parseInt(messageSenderId) === parseInt(currentUser.id)) {
        // Only update for add actions, not for remove actions
        if (data.action === 'add') {
            updateContactPreview(
                data.user_id, // reactor's ID
                null,
                data.user_id, // reactor's ID as sender
                new Date().toISOString(),
                true,
                data.user_name,
                data.emoji
            );

            // Move the contact to the top of the list
            const contactsList = document.querySelector('.contacts-body');
            const contactItem = document.querySelector(`.contact-item[data-user-id="${data.user_id}"]`);
            if (contactsList && contactItem) {
                contactsList.prepend(contactItem);
            }
        }
    }
});

socket.on('user_status_change', function(data) {
    // Update status in contact list
    const contactElement = document.querySelector(`.contact-item[data-user-id="${data.user_id}"]`);
    if (contactElement) {
        const statusDot = contactElement.querySelector('.status-indicator');
        if (statusDot) {
            statusDot.className = `status-indicator ${data.status}`;
        }
    }

    // Update status in chat header if this is the active chat
    const activeContact = document.querySelector('.contact-item.active');
    if (activeContact && activeContact.dataset.userId === data.user_id) {
        const statusElement = document.getElementById('currentChatStatus');
        if (statusElement) {
            statusElement.innerHTML = `
                <i class="fas fa-circle ${data.status === 'online' ? 'text-success' : 'text-secondary'} me-1"
                   style="font-size: 0.5rem;"></i>
                ${data.status}
            `;
        }
    }
});




function searchConversation() {
    const activeContact = document.querySelector('.contact-item.active');
    if (!activeContact) {
        console.error('No active contact found');
        return;
    }

    const userId = activeContact.dataset.userId;

    // Show the full profile panel
    const fullProfilePanel = document.getElementById('fullProfilePanel');
    fullProfilePanel.classList.add('active');

    // Update the content with search interface
    const contentArea = document.querySelector('.full-profile-content');
    contentArea.innerHTML = `
        <div class="search-conversation-container">
            <div class="search-header">
                <div class="search-input-wrapper">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text"
                           id="messageSearchInput"
                           class="search-input"
                           placeholder="Search in conversation..."
                           autocomplete="off">
                    <button class="clear-search" onclick="clearSearch()" style="display: none;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="search-filters">
                    <button class="filter-btn active" data-type="all">All</button>
                    <button class="filter-btn" data-type="messages">Messages</button>
                    <button class="filter-btn" data-type="media">Media</button>
                    <button class="filter-btn" data-type="files">Files</button>
                </div>
            </div>
            <div class="search-results" id="searchResults">
                <div class="initial-state">
                    <i class="fas fa-search"></i>
                    <p>Search messages, media and files</p>
                </div>
            </div>
        </div>
    `;

    // Setup event listeners
    const searchInput = document.getElementById('messageSearchInput');
    const clearButton = document.querySelector('.clear-search');
    const filterButtons = document.querySelectorAll('.filter-btn');
    let currentFilter = 'all';
    let searchTimeout;

    searchInput.addEventListener('input', function() {
        const query = this.value.trim();
        clearButton.style.display = query ? 'flex' : 'none';

        // Clear existing timeout
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        // Set new timeout to prevent too many requests
        searchTimeout = setTimeout(() => {
            if (query) {
                performSearch(query, currentFilter, userId);
            } else {
                showInitialState();
            }
        }, 300);
    });

    filterButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            filterButtons.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            currentFilter = this.dataset.type;

            const query = searchInput.value.trim();
            if (query) {
                performSearch(query, currentFilter, userId);
            }
        });
    });
}

function performSearch(query, filter, userId) {
    const searchResults = document.getElementById('searchResults');

    // Show loading state
    searchResults.innerHTML = `
        <div class="searching-state">
            <div class="spinner"></div>
            <p>Searching...</p>
        </div>
    `;

    // Encode parameters properly
    const params = new URLSearchParams({
        query: query.trim(),
        filter: filter,
        user_id: userId
    });

    fetch(`/search_conversation?${params.toString()}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (!data.success) {
                throw new Error(data.error || 'Search failed');
            }

            if (!data.results || data.results.length === 0) {
                showNoResults();
                return;
            }

            displaySearchResults(data.results, query);  // Pass the query parameter here
        })
        .catch(error => {
            console.error('Search error:', error);
            searchResults.innerHTML = `
                <div class="error-state">
                    <i class="fas fa-exclamation-circle"></i>
                    <p>Search error: ${error.message}</p>
                </div>
            `;
        });
}

function displaySearchResults(results, query) {  // Add query parameter here
    const searchResults = document.getElementById('searchResults');

    searchResults.innerHTML = `
        <div class="results-count">
            ${results.length} result${results.length !== 1 ? 's' : ''} found
        </div>
        <div class="results-list">
            ${results.map(result => `
                <div class="search-result-item" onclick="highlightMessage('${result.id}', '${query}')">
                    <div class="result-content">
                        <div class="result-message">${highlightSearchText(escapeHtml(result.content), query)}</div>
                        <div class="result-time">
                            <i class="far fa-clock"></i>
                            ${new Date(result.timestamp).toLocaleString()}
                            <span class="message-type">
                                ${result.type === 'sent' ?
                                    '<i class="fas fa-paper-plane"></i> Sent' :
                                    '<i class="fas fa-inbox"></i> Received'}
                            </span>
                        </div>
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

function clearSearch() {
    const searchInput = document.getElementById('messageSearchInput');
    const clearButton = document.querySelector('.clear-search');
    searchInput.value = '';
    clearButton.style.display = 'none';
    showInitialState();
}

function showInitialState() {
    const searchResults = document.getElementById('searchResults');
    searchResults.innerHTML = `
        <div class="initial-state">
            <i class="fas fa-search"></i>
            <p>Search messages, media and files</p>
        </div>
    `;
}

function showNoResults() {
    const searchResults = document.getElementById('searchResults');
    searchResults.innerHTML = `
        <div class="no-results-state">
            <i class="fas fa-search"></i>
            <p>No messages found</p>
        </div>
    `;
}

function highlightMessage(messageId, searchQuery) {
    const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
    if (messageElement) {
        // Remove existing highlights from all messages
        document.querySelectorAll('.message-highlighted').forEach(el => {
            el.classList.remove('message-highlighted');
        });

        // Add highlight to the found message
        messageElement.classList.add('message-highlighted');

        // Highlight the searched text within the message
        const messageText = messageElement.querySelector('.message-text');
        if (messageText) {
            const originalText = messageText.textContent;
            messageText.innerHTML = highlightSearchText(originalText, searchQuery);
        }

        // Scroll the message into view
        messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // Close the full profile panel
        const fullProfilePanel = document.getElementById('fullProfilePanel');
        if (fullProfilePanel) {
            fullProfilePanel.classList.remove('active');
            document.body.style.overflow = '';
            document.querySelector('.chat-container').classList.remove('blur-background');
        }
    }
}

function formatDate(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
}

function highlightSearchText(text, searchQuery) {
    if (!searchQuery) return text;
    const escapedQuery = searchQuery.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // Escape special characters
    const regex = new RegExp(`(${escapedQuery})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
}

function getFileIcon(fileType) {
    const iconMap = {
        'pdf': 'fa-file-pdf',
        'doc': 'fa-file-word',
        'docx': 'fa-file-word',
        'xls': 'fa-file-excel',
        'xlsx': 'fa-file-excel',
        'zip': 'fa-file-archive',
        'default': 'fa-file'
    };
    return iconMap[fileType] || iconMap.default;
}

// First, let's add a function to clear any existing panel content
function clearPanelContent() {
    const fullProfilePanel = document.getElementById('fullProfilePanel');
    const contentArea = fullProfilePanel.querySelector('.full-profile-content');
    if (contentArea) {
        contentArea.innerHTML = '';
    }
}



function viewFullProfile() {
    const activeContact = document.querySelector('.contact-item.active');
    if (!activeContact) return;

    const userId = activeContact.dataset.userId;
    const fullProfilePanel = document.getElementById('fullProfilePanel');

    // Show loading state
    showProfileLoadingState();

    // Fetch user details
    fetch(`/get_user_details/${userId}`)
        .then(response => response.json())
        .then(userData => {
            // Update profile content
            updateFullProfileContent(userData);

            // Show the panel with animation
            fullProfilePanel.classList.add('active');
            document.body.style.overflow = 'hidden';

            // Add blur effect to main content
            document.querySelector('.chat-container').classList.add('blur-background');
        })
        .catch(error => {
            console.error('Error fetching user details:', error);
            showProfileErrorState();
        });
}

function showProfileLoadingState() {
    const content = document.querySelector('.full-profile-content');
    content.innerHTML = `
        <div class="profile-loading">
            <div class="loading-spinner"></div>
            <p>Loading profile information...</p>
        </div>
    `;
}

function showProfileErrorState() {
    const content = document.querySelector('.full-profile-content');
    content.innerHTML = `
        <div class="profile-error">
            <i class="fas fa-exclamation-circle"></i>
            <p>Failed to load profile information</p>
            <button onclick="viewFullProfile()" class="retry-btn">
                <i class="fas fa-redo"></i> Retry
            </button>
        </div>
    `;
}

function updateFullProfileContent(userData) {
    const content = document.querySelector('.full-profile-content');
    const fullName = `${userData.first_name || ''} ${userData.last_name || ''}`.trim() || 'User';

    content.innerHTML = `
        <div class="profile-info-section">
            <div class="profile-photo-wrapper">
                <img src="/get_profile_photo/${userData.id}"
                     id="fullProfileAvatar"
                     class="profile-panel-avatar"
                     alt="${fullName}"
                     onerror="this.src='https://ui-avatars.com/api/?name=${encodeURIComponent(fullName)}&background=2563eb&color=fff&size=128'">
            </div>

            <div class="profile-header-info">
                <h2 class="profile-name-large">${fullName}</h2>
            </div>

            <div class="profile-details">
                <div class="info-item">
                    <span class="info-label">
                        <i class="fas fa-map-marker-alt"></i>
                        Country
                    </span>
                    <span class="info-value">${userData.country || 'Not specified'}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">
                        <i class="fas fa-briefcase"></i>
                        Position
                    </span>
                    <span class="info-value">${userData.position || 'Not specified'}</span>
                </div>
            </div>
        </div>`;
}

function generateActivityTimeline(activities) {
    if (!activities.length) {
        return '<div class="no-activity">No recent activity</div>';
    }

    return activities.map(activity => `
        <div class="timeline-item">
            <div class="timeline-icon">
                <i class="fas ${getActivityIcon(activity.type)}"></i>
            </div>
            <div class="timeline-content">
                <p class="timeline-text">${activity.description}</p>
                <span class="timeline-date">${formatDate(activity.date)}</span>
            </div>
        </div>
    `).join('');
}

function getActivityIcon(type) {
    const icons = {
        'project': 'fa-project-diagram',
        'message': 'fa-comment',
        'review': 'fa-star',
        'payment': 'fa-dollar-sign'
    };
    return icons[type] || 'fa-circle';
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function setupProfileTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Remove active class from all buttons
            tabButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            button.classList.add('active');
            // Handle tab content switching here
            // You can implement this based on your needs
        });
    });
}

function closeFullProfile() {
    const fullProfilePanel = document.getElementById('fullProfilePanel');
    fullProfilePanel.classList.remove('active');
    document.body.style.overflow = '';
    document.querySelector('.chat-container').classList.remove('blur-background');
}

function showSelectedFile(input) {
    const files = input.files;
    if (!files.length) {
        console.log('No file selected');
        return;
    }

    const file = files[0];
    console.log('File selected:', {
        name: file.name,
        size: file.size,
        type: file.type
    });

    // Size validation (25MB limit)
    if (file.size > 25 * 1024 * 1024) {
        alert('File size must be less than 25MB');
        input.value = '';
        return;
    }

    // Accept all common image formats
    const allowedImageTypes = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/webp',
        'image/bmp',
        'image/tiff'
    ];

    // Set the currentFile
    currentFile = file;

    // Create preview
    const previewContainer = document.createElement('div');
    previewContainer.className = 'file-preview-container';

    const previewItem = document.createElement('div');
    previewItem.className = 'file-preview-item';

    // Show preview based on file type
    if (file.type.startsWith('image/') || allowedImageTypes.includes(file.type)) {
        const reader = new FileReader();
        reader.onload = (e) => {
            previewItem.innerHTML = `
                <div class="preview-content">
                    <img src="${e.target.result}" alt="${file.name}" class="file-thumbnail">
                    <div class="file-info">
                        <span class="file-name" title="${file.name}">${file.name}</span>
                        <span class="file-size">${formatFileSize(file.size)}</span>
                    </div>
                    <button class="remove-file" onclick="removeSelectedFile()" title="Remove file">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        };
        reader.readAsDataURL(file);
    } else {
        previewItem.innerHTML = `
            <div class="preview-content">
                <i class="fas ${getFileIconClass(file.type)}"></i>
                <div class="file-info">
                    <span class="file-name" title="${file.name}">${file.name}</span>
                    <span class="file-size">${formatFileSize(file.size)}</span>
                </div>
                <button class="remove-file" onclick="removeSelectedFile()" title="Remove file">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
    }

    previewContainer.appendChild(previewItem);

    // Add preview to chat input area
    const chatInput = document.querySelector('.chat-input');
    const chatInputContainer = chatInput.parentElement;
    const existingPreview = chatInputContainer.querySelector('.file-preview-container');
    if (existingPreview) {
        chatInputContainer.removeChild(existingPreview);
    }
    chatInputContainer.insertBefore(previewContainer, chatInput);
}

function removeSelectedFile() {
    console.log('Removing selected file');
    currentFile = null;

    const fileInput = document.getElementById('file-upload');
    if (fileInput) {
        fileInput.value = '';
    }

    const previewContainer = document.querySelector('.file-preview-container');
    if (previewContainer) {
        previewContainer.remove();
    }
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

const reactionButton = document.createElement('button');
reactionButton.classList.add('message-react-btn');
reactionButton.innerHTML = '<i class="far fa-smile"></i>';
reactionButton.title = 'Add reaction';

// Create the plus button
const plusButton = document.createElement('button');
plusButton.classList.add('plus-reaction-button');
plusButton.innerHTML = '<i class="fas fa-plus"></i>';
plusButton.title = 'More reactions';

// Add click handler to the plus button
plusButton.onclick = (e) => {
    e.stopPropagation();
    const messageId = e.target.closest('.message').dataset.messageId;
    showExtendedEmojiPicker(e, messageId);
};

// Add both buttons to the message
const buttonContainer = document.createElement('div');
buttonContainer.classList.add('message-buttons');
buttonContainer.appendChild(reactionButton);
buttonContainer.appendChild(plusButton);

// Add the button container to the message
textMessage.appendChild(buttonContainer);

// Clear input and file selection
chatInput.value = '';
// Reset file input if needed
const fileInput = document.getElementById('file-upload');
if (fileInput) {
    fileInput.value = '';
}

// Add this function to handle starting a reply
function startReply(messageId, messageText) {
    console.log('Starting reply to message:', { messageId, messageText });

    const chatInput = document.querySelector('.chat-input');
    const existingReplyContainer = document.querySelector('.reply-container');

    if (existingReplyContainer) {
        existingReplyContainer.remove();
    }

    const replyContainer = document.createElement('div');
    replyContainer.classList.add('reply-container');
    replyContainer.dataset.replyToId = messageId; // Make sure this is set

    replyContainer.innerHTML = `
        <div class="reply-preview">
            <i class="fas fa-reply"></i>
            <span class="reply-text">${messageText}</span>
            <button class="cancel-reply" onclick="cancelReply()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    chatInput.parentElement.insertBefore(replyContainer, chatInput);
    chatInput.focus();

    // Verify reply container was created correctly
    console.log('Created reply container:', replyContainer);
    console.log('Reply to ID set:', replyContainer.dataset.replyToId);
}

// Add function to cancel reply
function cancelReply() {
    const replyContainer = document.querySelector('.reply-container');
    if (replyContainer) {
        replyContainer.remove();
    }
}



function loadContacts() {
    fetch('/get_contacts')
        .then(response => response.json())
        .then(contacts => {
            const contactsList = document.querySelector('.contacts-body');
            contactsList.innerHTML = ''; // Clear existing contacts

            contacts.forEach(contact => {
                // Create contact element
                const contactElement = createContactElement(contact);
                contactsList.appendChild(contactElement);
            });
        })
        .catch(error => console.error('Error loading contacts:', error));
}

function createContactElement(contact) {
    const contactItem = document.createElement('div');
    contactItem.className = 'contact-item';
    contactItem.dataset.userId = contact.id;
    contactItem.dataset.userName = `${contact.first_name} ${contact.last_name}`;

    // Set preview text based on activity type
    let previewText;
    if (contact.activity_type === 'reaction' && contact.reactor_name && contact.reaction_emoji) {
        previewText = `${contact.reactor_name} reacted ${contact.reaction_emoji} to your message`;
        contactItem.dataset.activityType = 'reaction';
        contactItem.dataset.reactorName = contact.reactor_name;
        contactItem.dataset.reactionEmoji = contact.reaction_emoji;
    } else if (contact.last_message) {
        const currentUserId = document.querySelector('meta[name="user-id"]').content;
        const isCurrentUser = parseInt(contact.last_sender_id) === parseInt(currentUserId);
        const name = isCurrentUser ? 'You' : contact.first_name;
        previewText = `${name}: ${contact.last_message}`;
    }

    contactItem.innerHTML = `
        <div class="contact-avatar">
            <img src="/get_profile_photo/${contact.id}" alt="${contact.first_name}"
                 onerror="this.src='static/default-avatar.png'">
        </div>
        <div class="contact-info">
            <div class="contact-name">${contact.first_name} ${contact.last_name}</div>
            <div class="contact-preview">
                <div class="preview-text">${previewText || ''}</div>
                <div class="message-time">${formatTimestamp(contact.last_activity_time)}</div>
            </div>
        </div>
    `;

    // Add click handler
    contactItem.onclick = () => switchContact(contactItem);

    return contactItem;
}

function updateContactPreview(contactId, messageText, senderId, messageTime, isReaction = false, reactorName = null, emoji = null) {
    const contactItem = document.querySelector(`.contact-item[data-user-id="${contactId}"]`);
    if (!contactItem) return;

    const previewTextElement = contactItem.querySelector('.preview-text');

    if (isReaction && reactorName && emoji) {
        // Use the full name for reactions
        previewTextElement.textContent = `${reactorName} reacted ${emoji} to your message`;
    } else if (messageText) {
        // Check if this is a reaction message
        if (messageText.includes('reacted') && messageText.includes('to your message')) {
            previewTextElement.textContent = messageText;
        } else {
            const currentUserId = document.querySelector('meta[name="user-id"]').content;
            const isCurrentUser = parseInt(senderId) === parseInt(currentUserId);
            const name = isCurrentUser ? 'You' : contactItem.querySelector('.contact-name').textContent.split(' ')[0];
            previewTextElement.textContent = `${name}: ${messageText}`;
        }
    }

    // Update timestamp if provided
    if (messageTime) {
        const timeElement = contactItem.querySelector('.contact-time');
        if (timeElement) {
            timeElement.textContent = formatTimestamp(messageTime);
        }
    }
}

function scrollToMessage(messageId) {
    console.log('Attempting to scroll to message:', messageId);
    const targetMessage = document.querySelector(`.message[data-message-id="${messageId}"]`);
    console.log('Found target message:', targetMessage);

    if (targetMessage) {
        targetMessage.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });

        // Make the highlight more noticeable
        targetMessage.style.backgroundColor = '#FFE58F';
        targetMessage.style.border = '2px solid #FFC107';

        setTimeout(() => {
            targetMessage.style.backgroundColor = '';
            targetMessage.style.border = '';
            targetMessage.style.transition = 'all 0.5s ease';
        }, 2000);
    } else {
        console.error('Target message not found:', messageId);
        // Log all messages in the chat for debugging
        const allMessages = document.querySelectorAll('.message');
        console.log('All message IDs in chat:', Array.from(allMessages).map(m => m.dataset.messageId));
    }
}

function deleteMessage(messageId) {
    if (!confirm('Are you sure you want to unsend this message?')) {
        return;
    }

    fetch('/delete_message', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            message_id: messageId
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    })
    .then(() => {
        // Find the message element
        const messageElement = document.querySelector(`.message[data-message-id="${messageId}"]`);
        if (messageElement) {
            // Update message content to show "Message unsent"
            const messageContent = messageElement.querySelector('.message-content');
            messageContent.innerHTML = `
                <p class="message-text deleted-message">Message unsent</p>
                <div class="message-meta">
                    <span class="message-time">${formatTimestamp(new Date())}</span>
                </div>
            `;

            // Add deleted message class
            messageElement.classList.add('message-deleted');

            // Remove reaction buttons and other interactive elements
            const reactionButtons = messageElement.querySelector('.reaction-buttons');
            if (reactionButtons) {
                reactionButtons.remove();
            }

            // Remove any existing reactions
            const reactionsContainer = messageElement.querySelector('.message-reactions');
            if (reactionsContainer) {
                reactionsContainer.innerHTML = '';
            }

            // Update contact preview
            const activeContact = document.querySelector('.contact-item.active');
            if (activeContact) {
                const contactId = activeContact.dataset.userId;
                updateContactPreview(contactId, 'Message unsent', getCurrentUserInfo().id, new Date().toISOString());
            }
        }
    })
    .catch(error => {
        console.error('Error deleting message:', error);
        alert('Failed to delete message. Please try again.');
    });
}

// Add this after your existing socket.io initialization code
// Initialize delete modal
let deleteModal = null;
let messageIdToDelete = null;

function initializeDeleteModal() {
    // Only create modal if it doesn't exist
    if (!document.getElementById('deleteMessageModal')) {
        const modalHTML = `
            <div class="modal fade" id="deleteMessageModal" tabindex="-1" aria-labelledby="deleteMessageModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="deleteMessageModalLabel">Delete Message</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <p>Are you sure you want to delete this message?</p>
                            <p class="text-muted small">This action cannot be undone.</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }
    deleteModal = new bootstrap.Modal(document.getElementById('deleteMessageModal'));
}

function showDeleteModal(messageId) {
    messageIdToDelete = messageId;
    if (!deleteModal) {
        initializeDeleteModal();
    }
    deleteModal.show();
}

// Add this to your existing DOMContentLoaded event listener
document.addEventListener('DOMContentLoaded', function() {
    // Your existing code remains unchanged

    // Initialize delete modal
    initializeDeleteModal();

    // Add event listener for delete confirmation
    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
        if (messageIdToDelete) {
            // Your existing delete message logic here
            fetch('/delete_message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message_id: messageIdToDelete
                })
            });
            deleteModal.hide();
            messageIdToDelete = null;
        }
    });
});


function viewFiles() {
    const activeContact = document.querySelector('.contact-item.active');
    if (!activeContact) {
        console.error('No active contact found');
        return;
    }

    const userId = activeContact.dataset.userId;

    // Show the full profile panel
    const fullProfilePanel = document.getElementById('fullProfilePanel');
    fullProfilePanel.classList.add('active');
    document.body.style.overflow = 'hidden';
    document.querySelector('.chat-container').classList.add('blur-background');

    // Update the panel content with files section
    const contentArea = document.querySelector('.full-profile-content');
    contentArea.innerHTML = `
        <div class="files-section">
            <div class="files-header">
                <h2>Shared Files</h2>
            </div>
            <div class="files-container" id="filesContainer">
                <div class="loading-files">
                    <div class="spinner"></div>
                    <p>Loading files...</p>
                </div>
            </div>
        </div>
    `;

    // Fetch and display files
    fetch(`/get_shared_files/${userId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(files => {
            if (files.error) {
                throw new Error(files.error);
            }
            displayFiles(files);
        })
        .catch(error => {
            console.error('Error fetching files:', error);
            showFilesError();
        });
}

function displayFiles(files) {
    const filesContainer = document.getElementById('filesContainer');

    if (!files || files.length === 0) {
        filesContainer.innerHTML = `
            <div class="no-files">
                <i class="fas fa-file-alt"></i>
                <p>No files shared yet</p>
            </div>
        `;
        return;
    }

    filesContainer.innerHTML = `
        <div class="files-grid">
            ${files.map(file => `
                <div class="file-card" onclick="downloadFile(${file.id}, '${file.file_name}')">
                    <div class="file-icon">
                        <i class="fas ${getFileIconClass(file.file_type)}"></i>
                    </div>
                    <div class="file-info">
                        <div class="file-name" title="${file.file_name}">${file.file_name}</div>
                        <div class="file-meta">
                            <span class="file-date">${formatDate(file.uploaded_at)}</span>
                        </div>
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

function showFilesError() {
    const filesContainer = document.getElementById('filesContainer');
    filesContainer.innerHTML = `
        <div class="files-error">
            <i class="fas fa-exclamation-circle"></i>
            <p>Failed to load files</p>
            <button onclick="viewFiles()" class="retry-btn">
                <i class="fas fa-redo"></i> Retry
            </button>
        </div>
    `;
}

function handleFileClick(fileUrl, fileType, fileName) {
    // Create modal if it doesn't exist
    let modal = document.getElementById('filePreviewModal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'filePreviewModal';
        modal.className = 'file-preview-modal';
        document.body.appendChild(modal);
    }

    // Determine content based on file type
    let content;
    if (fileType.includes('image')) {
        content = `
            <img src="${fileUrl}" alt="${fileName}" class="preview-content">
        `;
    } else if (fileType.includes('video')) {
        content = `
            <video controls class="preview-content">
                <source src="${fileUrl}" type="${fileType}">
                Your browser does not support video playback.
            </video>
        `;
    } else if (fileType.includes('audio')) {
        content = `
            <audio controls class="preview-content">
                <source src="${fileUrl}" type="${fileType}">
                Your browser does not support audio playback.
            </audio>
        `;
    } else {
        // For other file types, show download option
        content = `
            <div class="preview-content file-info-preview">
                <i class="${getFileIconClass(fileType)} file-preview-icon"></i>
                <h3>${fileName}</h3>
                <p>This file type cannot be previewed</p>
                <a href="${fileUrl}" class="download-button" download="${fileName}">
                    <i class="fas fa-download"></i> Download File
                </a>
            </div>
        `;
    }

    // Update modal content
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h4>${fileName}</h4>
                <button onclick="closeFilePreview()" class="close-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
            <div class="modal-footer">
                <a href="${fileUrl}" class="download-button" download="${fileName}">
                    <i class="fas fa-download"></i> Download
                </a>
            </div>
        </div>
    `;

    // Show modal
    modal.style.display = 'flex';

    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeFilePreview();
        }
    });
}

function closeFilePreview() {
    const modal = document.getElementById('filePreviewModal');
    if (modal) {
        modal.style.display = 'none';
    }
}






// Add click handler for replied messages
document.addEventListener('click', function(event) {
    const repliedMessage = event.target.closest('.replied-message');
    if (repliedMessage) {
        const replyToId = repliedMessage.getAttribute('data-reply-to-id');

        if (replyToId) {
            const targetMessage = document.querySelector(`.message[data-message-id="${replyToId}"]`);

            if (targetMessage) {
                // Scroll to the original message
                targetMessage.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });

                // Add highlight effect
                targetMessage.style.backgroundColor = '#FFE58F';
                targetMessage.style.border = '2px solid #FFC107';

                setTimeout(() => {
                    targetMessage.style.backgroundColor = '';
                    targetMessage.style.border = '';
                    targetMessage.style.transition = 'all 0.5s ease';
                }, 2000);
            }
        }
    }
});

// Image Viewer Functions
let imageViewerModal;
let currentZoom = 1;

// Initialize the image viewer modal
document.addEventListener('DOMContentLoaded', function() {
    imageViewerModal = new bootstrap.Modal(document.getElementById('imageViewerModal'));

    // Add zoom controls
    document.getElementById('zoomInBtn').addEventListener('click', function() {
        zoomImage(0.1);
    });

    document.getElementById('zoomOutBtn').addEventListener('click', function() {
        zoomImage(-0.1);
    });

    // Add download button functionality
    document.getElementById('downloadImageBtn').addEventListener('click', function() {
        const modalImage = document.getElementById('modalImage');
        const imageUrl = modalImage.src;
        const fileName = document.getElementById('imageFileName').textContent;

        // Create a temporary link to trigger download
        const a = document.createElement('a');
        a.href = imageUrl;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    });

    // Reset zoom when modal is closed
    document.getElementById('imageViewerModal').addEventListener('hidden.bs.modal', function() {
        resetZoom();
    });
});

// Function to open the image viewer
function openImageViewer(messageId, fileName) {
    // Set the image source
    const modalImage = document.getElementById('modalImage');
    modalImage.src = `/get_message_file/${messageId}`;

    // Set the file name
    document.getElementById('imageFileName').textContent = fileName;
    document.getElementById('imageViewerModalLabel').textContent = fileName;

    // Reset zoom
    resetZoom();

    // Show the modal
    imageViewerModal.show();
}

// Function to zoom the image
function zoomImage(delta) {
    const modalImage = document.getElementById('modalImage');
    currentZoom = Math.max(0.5, Math.min(3, currentZoom + delta));
    modalImage.style.transform = `scale(${currentZoom})`;
}

// Function to reset zoom
function resetZoom() {
    currentZoom = 1;
    const modalImage = document.getElementById('modalImage');
    modalImage.style.transform = 'scale(1)';
}

// Function to download a file
function downloadFile(messageId, fileName) {
    const a = document.createElement('a');
    a.href = `/get_message_file/${messageId}`;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
}







