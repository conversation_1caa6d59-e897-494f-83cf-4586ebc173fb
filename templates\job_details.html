<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ job.title }} - Job Details | GigGenius</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        :root {
            --blue: #004AAD;
            --pink: #CD208B;
            --light-gray: #f5f5f5;
            --medium-gray: #e0e0e0;
            --dark-gray: #6b7280;
            --white: #ffffff;
            --black: #333333;
            --header-height: 5.5rem;
            --container-width: 1200px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            background-color: #f9f9f9;
            margin: 0;
            padding: 0;
            width: 100%;
            overflow-x: hidden;
        }

        /* Container Styles */
        .container {
            width: 100%;
            max-width: 100%;
            margin: 0;
            padding: 0;
        }

        /* Navbar Styles */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 1rem;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 5.5rem;
            width: 100%;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 1.7rem;
            color: var(--pink);
            margin-right: 1rem;
        }

        .logo img {
            width: 5rem;
            height: 5rem;
        }

        .logo h1 {
            font-size: 1.7rem;
            margin-left: -0.5rem;
            font-weight: bold;
        }

        .nav-links, .nav-icons {
            display: flex;
            align-items: center;
        }

        .nav-links a, .nav-dropdown {
            margin: 0 0.5rem;
        }

        .nav-links a {
            color: #333;
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 0.75rem;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .nav-links a:hover {
            background-color: #f0f0f0;
        }

        /* Dropdown styles */
        .nav-dropdown {
            position: relative;
            display: inline-block;
        }

        .nav-dropbtn {
            color: #333;
            background: none;
            border: none;
            font-family: 'Poppins', sans-serif;
            font-weight: 500;
            padding: 0.5rem 0.75rem;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: background-color 0.3s;
        }

        .nav-dropbtn:hover {
            background-color: #f0f0f0;
        }

        .nav-dropdown-content {
            display: none;
            position: absolute;
            background-color: white;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            z-index: 1;
            border-radius: 8px;
            overflow: hidden;
        }

        .nav-dropdown-content a {
            color: #333;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            transition: background-color 0.3s;
        }

        .nav-dropdown-content a:hover {
            background-color: #f0f0f0;
        }

        .nav-dropdown:hover .nav-dropdown-content {
            display: block;
        }

        /* Nav icons */
        .nav-icons {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            margin-left: 2rem;
        }

        .notification-icon {
            position: relative;
            cursor: pointer;
        }

        .notification-icon i {
            font-size: 1.25rem;
            color: #555;
        }

        .notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: var(--pink);
            color: white;
            font-size: 0.7rem;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .profile-dropdown {
            position: relative;
        }

        .profile-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            cursor: pointer;
            border: 2px solid #e0e0e0;
        }

        .profile-button img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .profile-dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            background-color: white;
            min-width: 200px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            z-index: 1;
            border-radius: 8px;
            overflow: hidden;
        }

        .profile-dropdown-content a {
            color: #333;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            transition: background-color 0.3s;
        }

        .profile-dropdown-content a:hover {
            background-color: #f0f0f0;
        }

        .profile-dropdown-content i {
            margin-right: 8px;
            width: 20px;
            text-align: center;
        }

        .profile-dropdown:hover .profile-dropdown-content {
            display: block;
        }

        .dropdown-divider {
            height: 1px;
            background-color: #e0e0e0;
            margin: 8px 0;
        }

        .logout-option {
            color: #e53e3e !important;
        }

        .logout-option:hover {
            background-color: #fff5f5 !important;
        }

        /* Job details specific styles */
        .job-details-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 20px;
            font-family: 'Poppins', sans-serif;
            width: 100%;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .job-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 2rem;
        }

        .job-title-section h1 {
            font-size: 1.8rem;
            color: var(--black);
            margin-bottom: 1rem;
        }

        .job-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 1.5rem;
        }

        .job-meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--dark-gray);
        }

        .job-meta-item i {
            color: var(--blue);
        }

        .job-actions {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .apply-btn {
            background-color: var(--blue);
            color: var(--white);
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .apply-btn:hover {
            background-color: #003b8a;
        }

        .save-btn {
            background-color: var(--white);
            color: var(--blue);
            border: 2px solid var(--blue);
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }

        .save-btn:hover {
            background-color: #f0f7ff;
        }

        .job-content {
            padding: 2rem;
            background-color: white;
            border-radius: 8px;
        }

        .job-section {
            margin-bottom: 2rem;
        }

        .job-section h2 {
            font-size: 1.5rem;
            color: var(--black);
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--medium-gray);
        }

        .job-description {
            line-height: 1.6;
            color: var(--black);
            white-space: pre-line;
        }

        .skills-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
            margin-top: 1rem;
        }

        .skill-tag {
            background-color: #f0f7ff;
            color: var(--blue);
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.9rem;
        }

        .project-details {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }

        .project-detail-item {
            background-color: var(--light-gray);
            padding: 1.25rem;
            border-radius: 8px;
        }

        .project-detail-item h3 {
            font-size: 1rem;
            color: var(--dark-gray);
            margin-bottom: 0.5rem;
        }

        .project-detail-item p {
            font-size: 1.1rem;
            color: var(--black);
            font-weight: 500;
        }

        .budget-info {
            background-color: #f0f7ff;
            padding: 1.5rem;
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .budget-type {
            font-size: 1.1rem;
            color: var(--dark-gray);
        }

        .budget-amount {
            font-size: 1.5rem;
            color: var(--blue);
            font-weight: 600;
        }

        .client-info {
            display: flex;
            gap: 1.5rem;
            align-items: center;
            margin-top: 1rem;
        }

        .client-avatar {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            object-fit: cover;
        }

        .client-details h3 {
            font-size: 1.2rem;
            color: var(--black);
            margin-bottom: 0.25rem;
        }

        .client-details p {
            color: var(--dark-gray);
        }

        /* Footer */
        footer {
            background-color: var(--white);
            padding: 3rem 0 1.5rem;
            margin-top: 3rem;
            border-top: 1px solid var(--medium-gray);
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            font-size: 1.2rem;
            color: var(--black);
            margin-bottom: 1rem;
        }

        .footer-section a {
            display: block;
            color: var(--dark-gray);
            text-decoration: none;
            margin-bottom: 0.75rem;
            transition: color 0.3s;
        }

        .footer-section a:hover {
            color: var(--blue);
        }

        .footer-bottom {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--medium-gray);
        }

        .social-links {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .social-links a {
            color: var(--dark-gray);
            font-size: 1.2rem;
            transition: color 0.3s;
        }

        .social-links a:hover {
            color: var(--blue);
        }

        .footer-links {
            display: flex;
            gap: 1.5rem;
        }

        .footer-links a {
            color: var(--dark-gray);
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-links a:hover {
            color: var(--blue);
        }

        /* Responsive styles */
        @media (max-width: 768px) {
            .job-header {
                flex-direction: column;
                gap: 1.5rem;
            }

            .job-meta {
                flex-wrap: wrap;
            }

            .project-details {
                grid-template-columns: 1fr;
            }

            .budget-info {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }
        }

        main {
            width: 100%;
            max-width: 100%;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="container">
        <!-- Navbar -->
        <nav class="navbar">
            <div style="display: flex; align-items: center;">
                <a href="{{ url_for('landing_page') }}" style="text-decoration: none;">
                    <div class="logo">
                        <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                        GigGenius
                    </div>
                </a>
                <div class="nav-links">
                    <a href="{{ url_for('find_gigs') }}">Find Gigs</a>
                    <a href="{{ url_for('landing_page') }}">Proposals</a>

                    <!-- Log Work Hours Dropdown -->
                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Contracts
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('landing_page') }}">Log Hours</a>
                            <a href="{{ url_for('landing_page') }}">Work Diary</a>
                        </div>
                    </div>

                    <div class="nav-dropdown">
                        <button class="nav-dropbtn">Earnings
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="nav-dropdown-content">
                            <a href="{{ url_for('landing_page') }}">Billings and Earnings</a>
                            <a href="{{ url_for('landing_page') }}">Withdraw Earnings</a>
                            <a href="{{ url_for('landing_page') }}">Tax Info</a>
                        </div>
                    </div>

                    <a href="{{ url_for('landing_page') }}">Messages</a>
                </div>
                <div class="nav-icons">
                    <div class="notification-icon">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </div>
                    <div class="profile-dropdown">
                        <div class="profile-button">
                            <img src="{{ url_for('get_profile_photo', user_id=session.user_id) }}" alt="Profile Picture">
                        </div>
                        <div class="profile-dropdown-content">
                            <a href="{{ url_for('landing_page') }}">
                                <i class="fas fa-user"></i> My Profile
                            </a>
                            <a href="{{ url_for('landing_page') }}">
                                <i class="fas fa-cog"></i> Account Settings
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="{{ url_for('logout') }}" class="logout-option">
                                <i class="fas fa-sign-out-alt"></i> Log Out
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <main>
        <div class="container">
            <div class="job-details-container">
                <div class="job-header">
                    <div class="job-title-section">
                        <h1>{{ job.title }}</h1>
                        <div class="job-meta">
                            <div class="job-meta-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>{{ job.location|default('Remote') }}</span>
                            </div>
                            <div class="job-meta-item">
                                <i class="fas fa-briefcase"></i>
                                <span>{{ job.duration|default('Not specified') }}</span>
                            </div>
                            <div class="job-meta-item">
                                <i class="fas fa-user-graduate"></i>
                                <span>{{ job.experience_level|default('Any level') }}</span>
                            </div>
                            <div class="job-meta-item">
                                <i class="fas fa-clock"></i>
                                <span>Posted {{ job.created_at.strftime('%b %d, %Y') if job.created_at else 'Recently' }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="job-actions">
                        {% if has_applied %}
                            <button class="apply-btn applied" disabled>Applied</button>
                        {% else %}
                            <button class="apply-btn" id="apply-btn" data-job-id="{{ job.id }}" data-client-id="{{ job.client_id }}">Apply Now</button>
                        {% endif %}
                        <button class="save-btn">Save Job</button>
                    </div>
                </div>
                <div class="job-content">
                    <div class="job-section">
                        <h2>Job Description</h2>
                        <p class="job-description">{{ job.description }}</p>
                    </div>

                    <div class="job-section">
                        <h2>Skills</h2>
                        <div class="skills-list">
                            {% if skills %}
                                {% for skill in skills %}
                                    <div class="skill-tag">{{ skill }}</div>
                                {% endfor %}
                            {% else %}
                                <p>No specific skills listed for this job.</p>
                            {% endif %}
                        </div>
                    </div>

                    <div class="job-section">
                        <h2>Project Scope</h2>
                        <div class="project-details">
                            <div class="project-detail-item">
                                <h3>Project Size</h3>
                                <p>{{ job.project_size|default('Not specified') }}</p>
                            </div>
                            <div class="project-detail-item">
                                <h3>Duration</h3>
                                <p>{{ job.duration|default('Not specified') }}</p>
                            </div>
                            <div class="project-detail-item">
                                <h3>Experience Level</h3>
                                <p>{{ job.experience_level|default('Any level') }}</p>
                            </div>
                            <div class="project-detail-item">
                                <h3>Hiring Preference</h3>
                                <p>{{ job.hiring_preference|default('Not specified') }}</p>
                            </div>
                        </div>
                        {% if job.project_description %}
                            <div class="project-description" style="margin-top: 1.5rem;">
                                <h3 style="margin-bottom: 0.75rem;">Project Description</h3>
                                <p>{{ job.project_description }}</p>
                            </div>
                        {% endif %}
                    </div>

                    <div class="job-section">
                        <h2>Budget</h2>
                        <div class="budget-info">
                            <div class="budget-type">
                                {{ job.budget_type|title|default('Not specified') }}
                            </div>
                            <div class="budget-amount">
                                ${{ job.budget_amount|default('0') }}{% if job.budget_type == 'hourly' %}/hr{% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="job-section">
                        <h2>About the Client</h2>
                        <div class="client-info">
                            <img src="{{ client_photo_url }}" alt="Client" class="client-avatar">
                            <div class="client-details">
                                <h3>{{ job.business_name if job.business_name else job.first_name + ' ' + job.last_name }}</h3>
                                <p>{{ job.location|default('Location not specified') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>For Clients</h3>
                    <a href="#">How to Hire</a>
                    <a href="#">Marketplace</a>
                    <a href="#">Payroll Services</a>
                    <a href="#">Service Catalog</a>
                </div>
                <div class="footer-section">
                    <h3>For Geniuses</h3>
                    <a href="#">How It Works?</a>
                    <a href="#">Why Can't I Apply?</a>
                    <a href="#">Direct Contracts</a>
                    <a href="#">Find Mentors</a>
                </div>
                <div class="footer-section">
                    <h3>Resources</h3>
                    <a href="#">Help & Support</a>
                    <a href="#">News & Events</a>
                    <a href="#">Affiliate Program</a>
                </div>
                <div class="footer-section">
                    <h3>Company</h3>
                    <a href="#">About Us</a>
                    <a href="#">Contact Us</a>
                    <a href="#">Charity Projects</a>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="social-links">
                    <span>Follow Us:</span>
                    <a href="#"><i class="bi bi-facebook"></i></a>
                    <a href="#"><i class="bi bi-instagram"></i></a>
                    <a href="#"><i class="bi bi-twitter-x"></i></a>
                    <a href="#"><i class="bi bi-tiktok"></i></a>
                    <a href="#"><i class="bi bi-youtube"></i></a>
                    <a href="#"><i class="bi bi-linkedin"></i></a>
                </div>
                <p>©2025 GigGenius by <a href="https://genuinelysolutions.com/">Genuinely Business Solutions</a></p>
                <div class="footer-links">
                    <a href="#">Terms of Service</a>
                    <a href="#">Privacy Policy</a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const profileDropdown = document.querySelector('.profile-dropdown');
            const profileButton = profileDropdown.querySelector('.profile-button');

            // Toggle dropdown on profile button click
            profileButton.addEventListener('click', function(e) {
                e.stopPropagation();
                profileDropdown.classList.toggle('active');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!profileDropdown.contains(e.target)) {
                    profileDropdown.classList.remove('active');
                }
            });

            // Handle Apply Now button click
            const applyBtn = document.getElementById('apply-btn');
            if (applyBtn) {
                applyBtn.addEventListener('click', function() {
                    // Get job and client IDs from data attributes
                    const jobId = this.getAttribute('data-job-id');
                    const clientId = this.getAttribute('data-client-id');

                    if (!jobId || !clientId) {
                        alert('Missing job or client information');
                        return;
                    }

                    // Create form data
                    const formData = new FormData();
                    formData.append('job_id', jobId);
                    formData.append('client_id', clientId);

                    // Disable button to prevent multiple submissions
                    this.disabled = true;
                    this.textContent = 'Submitting...';

                    // Send application request
                    fetch('/submit_application', {
                        method: 'POST',
                        body: formData,
                        credentials: 'same-origin'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Show success message
                            alert('Application submitted successfully!');
                            // Update button
                            this.textContent = 'Applied';
                            this.classList.add('applied');
                        } else {
                            // Show error message
                            alert('Error: ' + data.error);
                            // Reset button
                            this.disabled = false;
                            this.textContent = 'Apply Now';
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred. Please try again.');
                        // Reset button
                        this.disabled = false;
                        this.textContent = 'Apply Now';
                    });
                });
            }
        });
    </script>

    <style>
        /* Style for applied button */
        .apply-btn.applied {
            background-color: #28a745;
            cursor: default;
        }
    </style>
</body>
</html>
