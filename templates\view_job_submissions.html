<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Job Board | GigGenius</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        :root {
            --blue: #004AAD;
            --pink: #CD208B;
            --light-gray: #f5f5f5;
            --medium-gray: #e0e0e0;
            --dark-gray: #6b7280;
            --white: #ffffff;
            --black: #333333;
            --success-green: #10B981;
            --warning-orange: #F59E0B;
            --danger-red: #EF4444;
            --header-height: 5.5rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background-color: #f9fafb;
            color: var(--black);
            line-height: 1.6;
        }

        /* Header Styles */
        .header {
            background-color: var(--white);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            z-index: 1000;
            height: var(--header-height);
        }

        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 100%;
            padding: 0 1rem;
        }

        .logo {
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .logo-text {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--blue);
            text-decoration: none;
        }

        .logo-text span {
            color: var(--pink);
        }

        /* Main Content */
        .main-content {
            margin-top: calc(var(--header-height) + 2rem);
            padding: 0 1rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        /* Page Title */
        .page-title {
            text-align: center;
            margin-bottom: 2rem;
        }

        .page-title h1 {
            font-size: 2.5rem;
            color: var(--blue);
            margin-bottom: 0.5rem;
        }

        .page-title p {
            color: var(--dark-gray);
            font-size: 1.1rem;
        }

        /* Search and Controls */
        .controls-container {
            background-color: var(--white);
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .search-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .search-box {
            flex: 1;
            min-width: 300px;
            padding: 0.75rem 1rem;
            border: 1px solid var(--medium-gray);
            border-radius: 8px;
            font-size: 1rem;
            background-color: var(--white);
            transition: all 0.3s;
        }

        .search-box:focus {
            outline: none;
            border-color: var(--blue);
            box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
        }

        .controls-buttons {
            display: flex;
            gap: 0.75rem;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1.25rem;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            font-size: 0.95rem;
        }

        .btn-blue {
            background-color: var(--blue);
            color: var(--white);
        }

        .btn-blue:hover {
            background-color: #003b8a;
        }

        .btn-pink {
            background-color: var(--pink);
            color: var(--white);
        }

        .btn-pink:hover {
            background-color: #b91c77;
        }

        /* Job Cards */
        .job-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
        }

        .job-card {
            background-color: var(--white);
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .job-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }

        .job-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--light-gray);
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }

        .client-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid var(--light-gray);
            background-color: var(--medium-gray);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            font-size: 1.5rem;
        }

        .job-title-client {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            width: 100%;
        }

        .client-info {
            display: flex;
            flex-direction: column;
            margin-bottom: 0.5rem;
        }

        .client-profile {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            background-color: #f8f9fa;
            padding: 0.75rem;
            border-radius: 8px;
            border-left: 3px solid var(--blue);
        }

        .job-title-container {
            margin-top: 0.75rem;
            padding-top: 0.5rem;
            border-top: 1px solid var(--light-gray);
        }

        .job-title-client h2 {
            font-size: 1.25rem;
            color: var(--black);
            margin: 0;
            font-weight: 600;
        }

        .client-name {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .client-name-text {
            font-weight: 600;
            color: var(--black);
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .client-name-text:before {
            content: '';
            display: inline-block;
            width: 10px;
            height: 10px;
            background-color: var(--blue);
            border-radius: 50%;
        }

        .client-full-name {
            font-size: 0.85rem;
            color: var(--dark-gray);
            margin-left: 1rem;
        }

        .client-location {
            margin-top: 0.25rem;
        }

        .location-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.15rem 0.5rem;
            background-color: rgba(0, 74, 173, 0.08);
            border-radius: 4px;
            font-size: 0.8rem;
            color: var(--blue);
            margin-left: 0;
        }

        .location-badge i {
            margin-right: 0.25rem;
            font-size: 0.75rem;
            color: var(--blue);
        }

        .job-body {
            padding: 1.5rem;
            flex: 1;
        }

        .job-description {
            color: var(--dark-gray);
            margin-bottom: 1.25rem;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .job-meta {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.75rem;
            margin-bottom: 1.25rem;
        }

        .job-meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            color: var(--dark-gray);
        }

        .job-meta-item i {
            color: var(--blue);
            width: 16px;
            text-align: center;
        }

        .job-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1.25rem;
        }

        .job-tag {
            background-color: #f0f7ff;
            color: var(--blue);
            padding: 0.25rem 0.75rem;
            border-radius: 50px;
            font-size: 0.8rem;
        }

        .job-footer {
            padding: 1.25rem;
            border-top: 1px solid var(--light-gray);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .job-price {
            font-weight: 600;
            color: var(--blue);
            font-size: 1.1rem;
        }

        .view-btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background-color: var(--pink);
            color: var(--white);
            border: none;
            border-radius: 50px;
            font-weight: 500;
            text-align: center;
            cursor: pointer;
            transition: background-color 0.2s;
            text-decoration: none;
        }

        .view-btn:hover {
            background-color: #b91c77;
        }

        .job-date {
            font-size: 0.85rem;
            color: var(--dark-gray);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* No Jobs Message */
        .no-jobs {
            text-align: center;
            padding: 3rem;
            background-color: var(--white);
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .no-jobs h2 {
            color: var(--dark-gray);
            margin-bottom: 1rem;
        }

        .no-jobs p {
            color: var(--dark-gray);
            margin-bottom: 1.5rem;
        }

        /* Modal */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: var(--white);
            margin: 5% auto;
            padding: 2rem;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
        }

        .close-modal {
            color: var(--dark-gray);
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.2s;
        }

        .close-modal:hover {
            color: var(--black);
        }

        .modal-title {
            margin-bottom: 1.5rem;
            color: var(--blue);
            font-size: 1.5rem;
            border-bottom: 1px solid var(--light-gray);
            padding-bottom: 0.75rem;
        }

        .modal-body {
            white-space: pre-wrap;
            line-height: 1.6;
            color: var(--dark-gray);
        }

        /* Modal Job Details Styling */
        .modal-job-header {
            margin-bottom: 1.5rem;
        }

        .modal-job-header h3 {
            font-size: 1.5rem;
            color: var(--black);
            margin-bottom: 0.5rem;
        }

        .modal-job-header p {
            color: var(--dark-gray);
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .modal-job-section {
            margin-bottom: 1.5rem;
            padding-bottom: 1.5rem;
            border-bottom: 1px solid var(--light-gray);
        }

        .modal-job-section:last-child {
            border-bottom: none;
            padding-bottom: 0;
        }

        .modal-job-section h4 {
            font-size: 1.1rem;
            color: var(--blue);
            margin-bottom: 0.75rem;
        }

        /* Modal Client Section */
        .modal-client-section {
            background-color: #f9fafb;
            padding: 1.25rem;
            border-radius: 8px;
        }

        .modal-client-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .modal-client-avatar {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid var(--light-gray);
            background-color: var(--medium-gray);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            font-size: 1.75rem;
        }

        .modal-client-details {
            flex: 1;
        }

        .modal-client-profile {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            border-left: 3px solid var(--blue);
            margin-bottom: 1rem;
        }

        .modal-client-name-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }

        .modal-client-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--black);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .modal-client-name:before {
            content: '';
            display: inline-block;
            width: 10px;
            height: 10px;
            background-color: var(--blue);
            border-radius: 50%;
        }

        .modal-client-full-name {
            font-size: 0.9rem;
            color: var(--dark-gray);
            margin: 0.25rem 0 0.5rem 1.5rem;
        }

        .modal-client-meta {
            margin-top: 0.5rem;
            padding-top: 0.5rem;
            border-top: 1px solid var(--light-gray);
        }

        .client-id-badge {
            background-color: var(--light-gray);
            color: var(--dark-gray);
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .modal-client-location {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--dark-gray);
            font-size: 0.9rem;
        }

        .modal-client-location {
            display: inline-flex;
            align-items: center;
            margin-left: 1.5rem;
            font-size: 0.85rem;
            color: var(--dark-gray);
        }

        .modal-client-location i,
        .modal-client-posted-date i {
            color: var(--blue);
            font-size: 0.85rem;
            width: 16px;
            text-align: center;
            margin-right: 0.25rem;
        }

        .modal-client-posted-date {
            display: flex;
            align-items: center;
            color: var(--dark-gray);
            font-size: 0.9rem;
        }

        .modal-job-section ul {
            list-style: none;
            padding: 0;
        }

        .modal-job-section li {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 0.5rem;
        }

        .modal-job-section li i {
            color: var(--blue);
            width: 16px;
            text-align: center;
        }

        .modal-skills {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .modal-skill-tag {
            background-color: #f0f7ff;
            color: var(--blue);
            padding: 0.25rem 0.75rem;
            border-radius: 50px;
            font-size: 0.9rem;
        }

        .modal-price {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--blue);
        }

        /* Pagination */
        .pagination {
            display: flex;
            justify-content: center;
            margin: 2rem 0;
        }

        .pagination button {
            margin: 0 0.25rem;
            padding: 0.5rem 1rem;
            border: 1px solid var(--medium-gray);
            background-color: var(--white);
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.2s;
        }

        .pagination button:hover {
            background-color: var(--light-gray);
        }

        .pagination button.active {
            background-color: var(--blue);
            color: var(--white);
            border-color: var(--blue);
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
            .search-row {
                flex-direction: column;
            }

            .job-cards {
                grid-template-columns: 1fr;
            }

            .job-meta {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <a href="/" class="logo">
                <div class="logo-text">Gig<span>Genius</span></div>
            </a>
            <div class="controls-buttons">
                <button class="btn btn-blue" onclick="window.location.reload()">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
                <button class="btn btn-pink" onclick="window.location.href='/'">
                    <i class="fas fa-home"></i> Home
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <div class="page-title">
                <h1>Available Jobs</h1>
                <p>Browse through the latest job opportunities</p>
            </div>

            <div class="controls-container">
                <div class="search-row">
                    <input type="text" id="searchInput" class="search-box" placeholder="Search for jobs by title, skills, or category...">
                </div>
            </div>

            <div class="job-cards" id="jobCards">
                {% if job_posts %}
                    {% for job in job_posts %}
                        <div class="job-card" data-id="{{ job.id }}" data-client-id="{{ job.client_id }}">
                            <div class="job-header">
                                <div class="client-avatar">
                                    {% if job.profile_photo_url %}
                                        <img src="{{ job.profile_photo_url }}" alt="Client" class="client-avatar">
                                    {% else %}
                                        <i class="fas fa-user"></i>
                                    {% endif %}
                                </div>
                                <div class="job-title-client">
                                    <div class="client-info">
                                        <div class="client-profile">
                                            <div class="client-name">
                                                <span class="client-name-text">
                                                    {% if job.business_name %}
                                                        {{ job.business_name }}
                                                    {% else %}
                                                        {{ job.first_name }} {{ job.last_name }}
                                                    {% endif %}
                                                </span>
                                                <span class="client-full-name">
                                                    {{ job.first_name }} {{ job.last_name }}
                                                </span>
                                            </div>
                                            <div class="client-location">
                                                {% if job.location %}
                                                    <span class="location-badge">
                                                        <i class="fas fa-map-marker-alt"></i> {{ job.location }}
                                                    </span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="job-title-container">
                                        <h2>{{ job.title }}</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="job-body">
                                <div class="job-description">
                                    {% if job.description %}
                                        {{ job.description[:150] }}...
                                    {% else %}
                                        No description available.
                                    {% endif %}
                                </div>
                                <div class="job-meta">
                                    {% if job.project_size %}
                                    <div class="job-meta-item">
                                        <i class="fas fa-chart-pie"></i>
                                        <span>{{ job.project_size }}</span>
                                    </div>
                                    {% endif %}

                                    {% if job.duration %}
                                    <div class="job-meta-item">
                                        <i class="fas fa-clock"></i>
                                        <span>{{ job.duration }}</span>
                                    </div>
                                    {% endif %}

                                    {% if job.experience_level %}
                                    <div class="job-meta-item">
                                        <i class="fas fa-star"></i>
                                        <span>{{ job.experience_level }}</span>
                                    </div>
                                    {% endif %}

                                    {% if job.category %}
                                    <div class="job-meta-item">
                                        <i class="fas fa-folder"></i>
                                        <span>{{ job.category }}</span>
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="job-tags">
                                    {% if job.skills %}
                                        {% set skill_list = job.skills.split(',') if ',' in job.skills else [job.skills] %}
                                        {% for skill in skill_list[:3] %}
                                            <span class="job-tag">{{ skill.strip() }}</span>
                                        {% endfor %}
                                        {% if skill_list|length > 3 %}
                                            <span class="job-tag">+{{ skill_list|length - 3 }} more</span>
                                        {% endif %}
                                    {% endif %}
                                </div>

                                <div class="job-date">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>Posted {{ job.created_at.strftime('%b %d, %Y') if job.created_at else 'N/A' }}</span>
                                </div>
                            </div>
                            <div class="job-footer">
                                <div class="job-price">
                                    {% if job.budget_type == 'Fixed Price' %}
                                        ${{ job.budget_amount|default('0') }} Fixed
                                    {% elif job.budget_type == 'Hourly Rate' %}
                                        ${{ job.budget_amount|default('0') }}/hr
                                    {% else %}
                                        ${{ job.budget_amount|default('0') }}
                                    {% endif %}
                                </div>
                                <button class="view-btn" onclick="showJobDetails('{{ job.id }}')">View Details</button>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="no-jobs">
                        <h2>No Jobs Found</h2>
                        <p>There are currently no job submissions in the database.</p>
                    </div>
                {% endif %}
            </div>

            <div class="pagination" id="pagination"></div>
        </div>
    </main>

    <!-- Modal for displaying full job details -->
    <div id="textModal" class="modal">
        <div class="modal-content">
            <span class="close-modal" onclick="closeModal()">&times;</span>
            <h2 id="modalTitle" class="modal-title"></h2>
            <div id="modalBody" class="modal-body"></div>
        </div>
    </div>

    <script>
        // Search functionality
        document.getElementById('searchInput').addEventListener('keyup', function() {
            const searchValue = this.value.toLowerCase();
            const jobCards = document.querySelectorAll('.job-card');

            jobCards.forEach(card => {
                const title = card.querySelector('.job-title-client h2').textContent.toLowerCase();
                const description = card.querySelector('.job-description').textContent.toLowerCase();
                const category = card.querySelector('.job-meta .fa-folder')?.parentElement?.textContent.toLowerCase() || '';
                const skills = Array.from(card.querySelectorAll('.job-tag')).map(tag => tag.textContent.toLowerCase()).join(' ');
                const client = card.querySelector('.client-name').textContent.toLowerCase();

                const searchableText = `${title} ${description} ${category} ${skills} ${client}`;

                if (searchableText.includes(searchValue)) {
                    card.style.display = '';
                } else {
                    card.style.display = 'none';
                }
            });

            // Check if no results and show message
            const visibleCards = document.querySelectorAll('.job-card[style="display: none;"]');
            const noResultsElement = document.querySelector('.no-jobs');

            if (visibleCards.length === jobCards.length && !noResultsElement) {
                const noResults = document.createElement('div');
                noResults.className = 'no-jobs';
                noResults.innerHTML = `
                    <h2>No Matching Jobs</h2>
                    <p>No jobs match your search criteria. Try different keywords.</p>
                `;
                document.getElementById('jobCards').appendChild(noResults);
            } else if (visibleCards.length < jobCards.length && noResultsElement) {
                noResultsElement.remove();
            }
        });

        // Modal functionality
        function showModal(title, content) {
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('modalBody').textContent = content;
            document.getElementById('textModal').style.display = 'block';
        }

        // Job details modal
        function showJobDetails(jobId) {
            // Find the job card with this ID
            const jobCard = document.querySelector(`.job-card[data-id="${jobId}"]`);

            if (!jobCard) {
                console.error('Job card not found for ID:', jobId);
                return;
            }

            // Get job details
            const title = jobCard.querySelector('.job-title-client h2').textContent;
            const clientNameElement = jobCard.querySelector('.client-name-text');
            const clientName = clientNameElement ? clientNameElement.textContent.trim() : 'Unknown Client';
            const clientFullName = jobCard.querySelector('.client-full-name').textContent.trim();
            const description = jobCard.querySelector('.job-description').textContent.trim();

            // Get client ID from the data attribute
            const clientId = jobCard.getAttribute('data-client-id') || 'N/A';

            // Get client location if available
            const locationBadge = jobCard.querySelector('.location-badge');
            const location = locationBadge ? locationBadge.textContent.trim() : '';

            // Get client avatar
            const avatarElement = jobCard.querySelector('.client-avatar');
            let avatarHTML = '';

            if (avatarElement.querySelector('img')) {
                const avatarSrc = avatarElement.querySelector('img').src;
                avatarHTML = `<img src="${avatarSrc}" alt="Client" class="modal-client-avatar">`;
            } else {
                avatarHTML = `<div class="modal-client-avatar"><i class="fas fa-user"></i></div>`;
            }

            // Build modal content
            let modalContent = `
                <div class="modal-job-header">
                    <h3>${title}</h3>
                </div>

                <div class="modal-job-section modal-client-section">
                    <h4>Posted by</h4>
                    <div class="modal-client-info">
                        ${avatarHTML}
                        <div class="modal-client-details">
                            <div class="modal-client-profile">
                                <div class="modal-client-name-row">
                                    <p class="modal-client-name">${clientName}</p>
                                    <span class="client-id-badge">ID: ${clientId}</span>
                                </div>
                                <p class="modal-client-full-name">${clientFullName}</p>
                                ${location ? `<p class="modal-client-location"><i class="fas fa-map-marker-alt"></i> ${location}</p>` : ''}
                            </div>
                            <div class="modal-client-meta">
                                <p class="modal-client-posted-date">
                                    <i class="fas fa-calendar-alt"></i>
                                    ${(() => {
                                        try {
                                            const dateText = jobCard.querySelector('.job-date').textContent.replace('Posted ', '');
                                            return 'Posted ' + new Date(dateText).toLocaleDateString('en-US', {year: 'numeric', month: 'long', day: 'numeric'});
                                        } catch (e) {
                                            return jobCard.querySelector('.job-date').textContent || 'Recently posted';
                                        }
                                    })()}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-job-section">
                    <h4>Description</h4>
                    <p>${description}</p>
                </div>
            `;

            // Add other job details if available
            const metaItems = jobCard.querySelectorAll('.job-meta-item');
            if (metaItems.length > 0) {
                modalContent += '<div class="modal-job-section"><h4>Job Details</h4><ul>';
                metaItems.forEach(item => {
                    const icon = item.querySelector('i').className;
                    const text = item.querySelector('span').textContent;
                    modalContent += `<li><i class="${icon}"></i> ${text}</li>`;
                });
                modalContent += '</ul></div>';
            }

            // Add skills if available
            const skills = jobCard.querySelectorAll('.job-tag');
            if (skills.length > 0) {
                modalContent += '<div class="modal-job-section"><h4>Skills Required</h4><div class="modal-skills">';
                skills.forEach(skill => {
                    if (!skill.textContent.includes('+')) { // Don't include the "+X more" tag
                        modalContent += `<span class="modal-skill-tag">${skill.textContent}</span>`;
                    }
                });
                modalContent += '</div></div>';
            }

            // Add budget information
            const price = jobCard.querySelector('.job-price').textContent.trim();
            modalContent += `
                <div class="modal-job-section">
                    <h4>Budget</h4>
                    <p class="modal-price">${price}</p>
                </div>
            `;

            // Show the modal
            document.getElementById('modalTitle').textContent = 'Job Details';
            document.getElementById('modalBody').innerHTML = modalContent;
            document.getElementById('textModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('textModal').style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('textModal');
            if (event.target == modal) {
                closeModal();
            }
        }

        // Pagination for job cards
        document.addEventListener('DOMContentLoaded', function() {
            const jobCards = document.querySelectorAll('.job-card');
            const cardsPerPage = 9; // Show 9 cards per page (3x3 grid)
            const pageCount = Math.ceil(jobCards.length / cardsPerPage);
            const paginationContainer = document.getElementById('pagination');

            if (jobCards.length <= cardsPerPage) {
                return; // No pagination needed
            }

            // Create pagination buttons
            for (let i = 1; i <= pageCount; i++) {
                const btn = document.createElement('button');
                btn.textContent = i;
                btn.addEventListener('click', function() {
                    showPage(i);
                    document.querySelectorAll('.pagination button').forEach(b => {
                        b.classList.remove('active');
                    });
                    this.classList.add('active');
                });
                paginationContainer.appendChild(btn);
            }

            // Show first page by default
            document.querySelector('.pagination button').classList.add('active');
            showPage(1);

            function showPage(pageNum) {
                const start = (pageNum - 1) * cardsPerPage;
                const end = start + cardsPerPage;

                jobCards.forEach((card, index) => {
                    card.style.display = (index >= start && index < end) ? '' : 'none';
                });
            }
        });

        // Add job ID data attribute to each card for easier reference
        document.addEventListener('DOMContentLoaded', function() {
            const jobCards = document.querySelectorAll('.job-card');
            jobCards.forEach((card, index) => {
                // Use index+1 as fallback ID if not available from the data
                card.setAttribute('data-id', index + 1);
            });
        });
    </script>
</body>
</html>



